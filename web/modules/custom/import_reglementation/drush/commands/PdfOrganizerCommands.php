<?php

namespace Drupal\import_reglementation\Commands;

use Dr<PERSON><PERSON>\import_reglementation\Service\PdfOrganizer;
use Drush\Commands\DrushCommands;
use Drush\Attributes as CLI;

/**
 * Commandes Drush pour organiser les fichiers PDF.
 */
class PdfOrganizerCommands extends DrushCommands {

  /**
   * Le service PdfOrganizer.
   *
   * @var \Drupal\import_reglementation\Service\PdfOrganizer
   */
  protected $pdfOrganizer;

  /**
   * Constructs a new PdfOrganizerCommands object.
   *
   * @param \Drupal\import_reglementation\Service\PdfOrganizer $pdf_organizer
   *   Le service PdfOrganizer.
   */
  public function __construct(PdfOrganizer $pdf_organizer) {
    $this->pdfOrganizer = $pdf_organizer;
  }

  /**
   * Analyse un nom de fichier PDF pour extraire les informations.
   *
   * @param string $filename
   *   Le nom du fichier à analyser.
   *
   * @command pdf:analyze
   * @aliases pdf-analyze
   * @usage pdf:analyze "Arrêté 20.80 Fr.pdf"
   *   Analyse le nom de fichier spécifié.
   */
  #[CLI\Command(name: 'pdf:analyze', aliases: ['pdf-analyze'])]
  #[CLI\Argument(name: 'filename', description: 'Nom du fichier à analyser')]
  #[CLI\Usage(name: 'pdf:analyze "Arrêté 20.80 Fr.pdf"', description: 'Analyse le nom de fichier spécifié')]
  public function analyzeFilename($filename) {
    $this->output()->writeln("Analyse du fichier: <info>$filename</info>");
    
    $result = $this->pdfOrganizer->analyzeFilename($filename);
    
    if ($result['valid']) {
      $this->output()->writeln('<fg=green>✓ Analyse réussie</fg=green>');
      $this->output()->writeln("  Type de réglementation: <comment>{$result['type']}</comment>");
      $this->output()->writeln("  Numéro de texte: <comment>{$result['numero']}</comment>");
      $this->output()->writeln("  Langue: <comment>{$result['langue']}</comment>");
      
      $destination_path = $this->pdfOrganizer->generateDestinationPath($result);
      $this->output()->writeln("  Chemin de destination: <comment>$destination_path</comment>");
    } else {
      $this->output()->writeln('<fg=red>✗ Impossible d\'analyser le nom de fichier</fg=red>');
    }
  }

  /**
   * Organise un fichier PDF dans la structure appropriée.
   *
   * @param string $file_path
   *   Chemin vers le fichier à organiser.
   * @param array $options
   *   Options de la commande.
   *
   * @command pdf:organize-file
   * @aliases pdf-organize-file
   * @option move Déplacer le fichier au lieu de le copier
   * @usage pdf:organize-file /path/to/file.pdf
   *   Organise le fichier spécifié (copie par défaut).
   * @usage pdf:organize-file /path/to/file.pdf --move
   *   Organise le fichier spécifié en le déplaçant.
   */
  #[CLI\Command(name: 'pdf:organize-file', aliases: ['pdf-organize-file'])]
  #[CLI\Argument(name: 'file_path', description: 'Chemin vers le fichier à organiser')]
  #[CLI\Option(name: 'move', description: 'Déplacer le fichier au lieu de le copier')]
  #[CLI\Usage(name: 'pdf:organize-file /path/to/file.pdf', description: 'Organise le fichier spécifié (copie par défaut)')]
  #[CLI\Usage(name: 'pdf:organize-file /path/to/file.pdf --move', description: 'Organise le fichier spécifié en le déplaçant')]
  public function organizeFile($file_path, array $options = ['move' => FALSE]) {
    $move = $options['move'];
    $action = $move ? 'déplacement' : 'copie';
    
    $this->output()->writeln("Organisation du fichier: <info>$file_path</info> ($action)");
    
    if (!file_exists($file_path)) {
      $this->output()->writeln('<fg=red>✗ Le fichier n\'existe pas</fg=red>');
      return;
    }
    
    $result = $this->pdfOrganizer->organizeFile($file_path, $move);
    
    if ($result['success']) {
      $this->output()->writeln('<fg=green>✓ Succès</fg=green>');
      $this->output()->writeln("  Message: <comment>{$result['message']}</comment>");
      $this->output()->writeln("  Destination: <comment>{$result['destination_path']}</comment>");
      
      if (!empty($result['file_info'])) {
        $info = $result['file_info'];
        $this->output()->writeln("  Informations détectées:");
        $this->output()->writeln("    - Type: <comment>{$info['type']}</comment>");
        $this->output()->writeln("    - Numéro: <comment>{$info['numero']}</comment>");
        $this->output()->writeln("    - Langue: <comment>{$info['langue']}</comment>");
      }
    } else {
      $this->output()->writeln('<fg=red>✗ Échec</fg=red>');
      $this->output()->writeln("  Message: <comment>{$result['message']}</comment>");
    }
  }

  /**
   * Organise tous les fichiers PDF d'un dossier.
   *
   * @param string $source_directory
   *   Dossier source contenant les fichiers PDF.
   * @param array $options
   *   Options de la commande.
   *
   * @command pdf:organize-directory
   * @aliases pdf-organize-dir
   * @option move Déplacer les fichiers au lieu de les copier
   * @option recursive Traiter les sous-dossiers récursivement
   * @usage pdf:organize-directory /path/to/source/directory
   *   Organise tous les PDF du dossier (copie par défaut).
   * @usage pdf:organize-directory /path/to/source/directory --move --recursive
   *   Organise tous les PDF du dossier et sous-dossiers en les déplaçant.
   */
  #[CLI\Command(name: 'pdf:organize-directory', aliases: ['pdf-organize-dir'])]
  #[CLI\Argument(name: 'source_directory', description: 'Dossier source contenant les fichiers PDF')]
  #[CLI\Option(name: 'move', description: 'Déplacer les fichiers au lieu de les copier')]
  #[CLI\Option(name: 'recursive', description: 'Traiter les sous-dossiers récursivement')]
  #[CLI\Usage(name: 'pdf:organize-directory /path/to/source/directory', description: 'Organise tous les PDF du dossier (copie par défaut)')]
  #[CLI\Usage(name: 'pdf:organize-directory /path/to/source/directory --move --recursive', description: 'Organise tous les PDF du dossier et sous-dossiers en les déplaçant')]
  public function organizeDirectory($source_directory, array $options = ['move' => FALSE, 'recursive' => FALSE]) {
    $move = $options['move'];
    $recursive = $options['recursive'];
    $action = $move ? 'déplacement' : 'copie';
    $recursion = $recursive ? 'récursif' : 'non-récursif';
    
    $this->output()->writeln("Organisation du dossier: <info>$source_directory</info> ($action, $recursion)");
    
    if (!is_dir($source_directory)) {
      $this->output()->writeln('<fg=red>✗ Le dossier n\'existe pas</fg=red>');
      return;
    }
    
    $results = $this->pdfOrganizer->organizeDirectory($source_directory, $move, $recursive);
    
    $this->output()->writeln("\n<fg=green>Résultats de l'organisation:</fg=green>");
    $this->output()->writeln("  Total de fichiers traités: <comment>{$results['total']}</comment>");
    $this->output()->writeln("  Succès: <comment>{$results['success']}</comment>");
    $this->output()->writeln("  Erreurs: <comment>{$results['errors']}</comment>");
    
    if (!empty($results['details'])) {
      $this->output()->writeln("\n<fg=yellow>Détails:</fg=yellow>");
      foreach ($results['details'] as $detail) {
        $status = $detail['result']['success'] ? '<fg=green>✓</fg=green>' : '<fg=red>✗</fg=red>';
        $this->output()->writeln("  $status {$detail['file']}");
        
        if (!$detail['result']['success']) {
          $this->output()->writeln("    Erreur: <comment>{$detail['result']['message']}</comment>");
        }
      }
    }
  }

  /**
   * Recherche des fichiers PDF par numéro de texte et type.
   *
   * @param string $numero
   *   Numéro de texte à rechercher.
   * @param string $type
   *   Type de réglementation.
   * @param array $options
   *   Options de la commande.
   *
   * @command pdf:find
   * @aliases pdf-find
   * @option langue Langue spécifique (Fr ou AR)
   * @usage pdf:find "20.80" "Arrêté"
   *   Recherche l'Arrêté 20.80 dans toutes les langues.
   * @usage pdf:find "116.14" "Loi" --langue=Fr
   *   Recherche la Loi 116.14 en français uniquement.
   */
  #[CLI\Command(name: 'pdf:find', aliases: ['pdf-find'])]
  #[CLI\Argument(name: 'numero', description: 'Numéro de texte à rechercher')]
  #[CLI\Argument(name: 'type', description: 'Type de réglementation')]
  #[CLI\Option(name: 'langue', description: 'Langue spécifique (Fr ou AR)')]
  #[CLI\Usage(name: 'pdf:find "20.80" "Arrêté"', description: 'Recherche l\'Arrêté 20.80 dans toutes les langues')]
  #[CLI\Usage(name: 'pdf:find "116.14" "Loi" --langue=Fr', description: 'Recherche la Loi 116.14 en français uniquement')]
  public function findFile($numero, $type, array $options = ['langue' => '']) {
    $langue = $options['langue'];
    
    $search_info = "Recherche: <info>$type $numero</info>";
    if (!empty($langue)) {
      $search_info .= " (langue: <info>$langue</info>)";
    }
    $this->output()->writeln($search_info);
    
    $found_files = $this->pdfOrganizer->findFileByNumber($numero, $type, $langue);
    
    if (!empty($found_files)) {
      $this->output()->writeln("<fg=green>✓ Trouvé " . count($found_files) . " fichier(s):</fg=green>");
      
      foreach ($found_files as $file) {
        $this->output()->writeln("  📄 <comment>{$file['filename']}</comment>");
        $this->output()->writeln("     Langue: <info>{$file['info']['langue']}</info>");
        $this->output()->writeln("     Chemin: <comment>{$file['path']}</comment>");
        $this->output()->writeln("");
      }
    } else {
      $this->output()->writeln('<fg=red>✗ Aucun fichier trouvé</fg=red>');
    }
  }

  /**
   * Affiche la structure actuelle des dossiers PDF.
   *
   * @command pdf:structure
   * @aliases pdf-structure
   * @usage pdf:structure
   *   Affiche la structure des dossiers PDF.
   */
  #[CLI\Command(name: 'pdf:structure', aliases: ['pdf-structure'])]
  #[CLI\Usage(name: 'pdf:structure', description: 'Affiche la structure des dossiers PDF')]
  public function showStructure() {
    $this->output()->writeln("<fg=green>Structure des dossiers PDF:</fg=green>");
    
    $pdf_base_path = \Drupal::service('extension.list.module')->getPath('import_reglementation') . '/pdf';
    $this->output()->writeln("Chemin de base: <comment>$pdf_base_path</comment>\n");
    
    $this->displayDirectoryStructure($pdf_base_path, 0);
  }

  /**
   * Affiche la structure d'un dossier de manière récursive.
   *
   * @param string $path
   *   Chemin du dossier.
   * @param int $level
   *   Niveau de profondeur.
   */
  protected function displayDirectoryStructure($path, $level = 0) {
    if (!is_dir($path)) {
      return;
    }
    
    $indent = str_repeat('  ', $level);
    $items = scandir($path);
    
    foreach ($items as $item) {
      if ($item === '.' || $item === '..') {
        continue;
      }
      
      $item_path = $path . '/' . $item;
      
      if (is_dir($item_path)) {
        $this->output()->writeln($indent . "📁 <info>$item/</info>");
        if ($level < 3) { // Limiter la profondeur
          $this->displayDirectoryStructure($item_path, $level + 1);
        }
      } else {
        $this->output()->writeln($indent . "📄 <comment>$item</comment>");
      }
    }
  }
}
