<?php

namespace Drupal\import_reglementation\Commands;

use Dr<PERSON><PERSON>\import_reglementation\Service\PdfOrganizer;
use Drush\Commands\DrushCommands;
use Drush\Attributes as CLI;

/**
 * Commandes Drush pour organiser les fichiers PDF.
 */
class PdfOrganizerCommands extends DrushCommands {

  /**
   * Le service PdfOrganizer.
   *
   * @var \Drupal\import_reglementation\Service\PdfOrganizer
   */
  protected $pdfOrganizer;

  /**
   * Constructs a new PdfOrganizerCommands object.
   *
   * @param \Drupal\import_reglementation\Service\PdfOrganizer $pdf_organizer
   *   Le service PdfOrganizer.
   */
  public function __construct(PdfOrganizer $pdf_organizer) {
    $this->pdfOrganizer = $pdf_organizer;
  }

  /**
   * Analyse un nom de fichier PDF pour extraire les informations.
   *
   * @param string $filename
   *   Le nom du fichier à analyser.
   *
   * @command pdf:analyze
   * @aliases pdf-analyze
   * @usage pdf:analyze "Arrêté 20.80 Fr.pdf"
   *   Analyse le nom de fichier spécifié.
   */
  #[CLI\Command(name: 'pdf:analyze', aliases: ['pdf-analyze'])]
  #[CLI\Argument(name: 'filename', description: 'Nom du fichier à analyser')]
  #[CLI\Usage(name: 'pdf:analyze "Arrêté 20.80 Fr.pdf"', description: 'Analyse le nom de fichier spécifié')]
  public function analyzeFilename($filename) {
    $this->output()->writeln("Analyse du fichier: <info>$filename</info>");

    $result = $this->pdfOrganizer->analyzeFilename($filename);

    if ($result['valid']) {
      $this->output()->writeln('<fg=green>✓ Analyse réussie</fg=green>');
      $this->output()->writeln("  Type de réglementation: <comment>{$result['type']}</comment>");
      $this->output()->writeln("  Numéro de texte: <comment>{$result['numero']}</comment>");
      $this->output()->writeln("  Langue: <comment>{$result['langue']}</comment>");

      $destination_path = $this->pdfOrganizer->generateDestinationPath($result);
      $this->output()->writeln("  Chemin de destination: <comment>$destination_path</comment>");
    } else {
      $this->output()->writeln('<fg=red>✗ Impossible d\'analyser le nom de fichier</fg=red>');
    }
  }

  /**
   * Organise un fichier PDF dans la structure appropriée.
   *
   * @param string $file_path
   *   Chemin vers le fichier à organiser.
   * @param array $options
   *   Options de la commande.
   *
   * @command pdf:organize-file
   * @aliases pdf-organize-file
   * @option move Déplacer le fichier au lieu de le copier
   * @usage pdf:organize-file /path/to/file.pdf
   *   Organise le fichier spécifié (copie par défaut).
   * @usage pdf:organize-file /path/to/file.pdf --move
   *   Organise le fichier spécifié en le déplaçant.
   */
  #[CLI\Command(name: 'pdf:organize-file', aliases: ['pdf-organize-file'])]
  #[CLI\Argument(name: 'file_path', description: 'Chemin vers le fichier à organiser')]
  #[CLI\Option(name: 'move', description: 'Déplacer le fichier au lieu de le copier')]
  #[CLI\Usage(name: 'pdf:organize-file /path/to/file.pdf', description: 'Organise le fichier spécifié (copie par défaut)')]
  #[CLI\Usage(name: 'pdf:organize-file /path/to/file.pdf --move', description: 'Organise le fichier spécifié en le déplaçant')]
  public function organizeFile($file_path, array $options = ['move' => FALSE]) {
    $move = $options['move'];
    $action = $move ? 'déplacement' : 'copie';

    $this->output()->writeln("Organisation du fichier: <info>$file_path</info> ($action)");

    if (!file_exists($file_path)) {
      $this->output()->writeln('<fg=red>✗ Le fichier n\'existe pas</fg=red>');
      return;
    }

    $result = $this->pdfOrganizer->organizeFile($file_path, $move);

    if ($result['success']) {
      $this->output()->writeln('<fg=green>✓ Succès</fg=green>');
      $this->output()->writeln("  Message: <comment>{$result['message']}</comment>");
      $this->output()->writeln("  Destination: <comment>{$result['destination_path']}</comment>");

      if (!empty($result['file_info'])) {
        $info = $result['file_info'];
        $this->output()->writeln("  Informations détectées:");
        $this->output()->writeln("    - Type: <comment>{$info['type']}</comment>");
        $this->output()->writeln("    - Numéro: <comment>{$info['numero']}</comment>");
        $this->output()->writeln("    - Langue: <comment>{$info['langue']}</comment>");
      }
    } else {
      $this->output()->writeln('<fg=red>✗ Échec</fg=red>');
      $this->output()->writeln("  Message: <comment>{$result['message']}</comment>");
    }
  }

  /**
   * Organise tous les fichiers PDF d'un dossier.
   *
   * @param string $source_directory
   *   Dossier source contenant les fichiers PDF.
   * @param array $options
   *   Options de la commande.
   *
   * @command pdf:organize-directory
   * @aliases pdf-organize-dir
   * @option move Déplacer les fichiers au lieu de les copier
   * @option recursive Traiter les sous-dossiers récursivement
   * @usage pdf:organize-directory /path/to/source/directory
   *   Organise tous les PDF du dossier (copie par défaut).
   * @usage pdf:organize-directory /path/to/source/directory --move --recursive
   *   Organise tous les PDF du dossier et sous-dossiers en les déplaçant.
   */
  #[CLI\Command(name: 'pdf:organize-directory', aliases: ['pdf-organize-dir'])]
  #[CLI\Argument(name: 'source_directory', description: 'Dossier source contenant les fichiers PDF')]
  #[CLI\Option(name: 'move', description: 'Déplacer les fichiers au lieu de les copier')]
  #[CLI\Option(name: 'recursive', description: 'Traiter les sous-dossiers récursivement')]
  #[CLI\Usage(name: 'pdf:organize-directory /path/to/source/directory', description: 'Organise tous les PDF du dossier (copie par défaut)')]
  #[CLI\Usage(name: 'pdf:organize-directory /path/to/source/directory --move --recursive', description: 'Organise tous les PDF du dossier et sous-dossiers en les déplaçant')]
  public function organizeDirectory($source_directory, array $options = ['move' => FALSE, 'recursive' => FALSE]) {
    $move = $options['move'];
    $recursive = $options['recursive'];
    $action = $move ? 'déplacement' : 'copie';
    $recursion = $recursive ? 'récursif' : 'non-récursif';

    $this->output()->writeln("Organisation du dossier: <info>$source_directory</info> ($action, $recursion)");

    if (!is_dir($source_directory)) {
      $this->output()->writeln('<fg=red>✗ Le dossier n\'existe pas</fg=red>');
      return;
    }

    $results = $this->pdfOrganizer->organizeDirectory($source_directory, $move, $recursive);

    $this->output()->writeln("\n<fg=green>Résultats de l'organisation:</fg=green>");
    $this->output()->writeln("  Total de fichiers traités: <comment>{$results['total']}</comment>");
    $this->output()->writeln("  Succès: <comment>{$results['success']}</comment>");
    $this->output()->writeln("  Erreurs: <comment>{$results['errors']}</comment>");

    if (!empty($results['details'])) {
      $this->output()->writeln("\n<fg=yellow>Détails:</fg=yellow>");
      foreach ($results['details'] as $detail) {
        $status = $detail['result']['success'] ? '<fg=green>✓</fg=green>' : '<fg=red>✗</fg=red>';
        $this->output()->writeln("  $status {$detail['file']}");

        if (!$detail['result']['success']) {
          $this->output()->writeln("    Erreur: <comment>{$detail['result']['message']}</comment>");
        }
      }
    }
  }

  /**
   * Recherche des fichiers PDF par numéro de texte et type.
   *
   * @param string $numero
   *   Numéro de texte à rechercher.
   * @param string $type
   *   Type de réglementation.
   * @param array $options
   *   Options de la commande.
   *
   * @command pdf:find
   * @aliases pdf-find
   * @option langue Langue spécifique (Fr ou AR)
   * @usage pdf:find "20.80" "Arrêté"
   *   Recherche l'Arrêté 20.80 dans toutes les langues.
   * @usage pdf:find "116.14" "Loi" --langue=Fr
   *   Recherche la Loi 116.14 en français uniquement.
   */
  #[CLI\Command(name: 'pdf:find', aliases: ['pdf-find'])]
  #[CLI\Argument(name: 'numero', description: 'Numéro de texte à rechercher')]
  #[CLI\Argument(name: 'type', description: 'Type de réglementation')]
  #[CLI\Option(name: 'langue', description: 'Langue spécifique (Fr ou AR)')]
  #[CLI\Usage(name: 'pdf:find "20.80" "Arrêté"', description: 'Recherche l\'Arrêté 20.80 dans toutes les langues')]
  #[CLI\Usage(name: 'pdf:find "116.14" "Loi" --langue=Fr', description: 'Recherche la Loi 116.14 en français uniquement')]
  public function findFile($numero, $type, array $options = ['langue' => '']) {
    $langue = $options['langue'];

    $search_info = "Recherche: <info>$type $numero</info>";
    if (!empty($langue)) {
      $search_info .= " (langue: <info>$langue</info>)";
    }
    $this->output()->writeln($search_info);

    $found_files = $this->pdfOrganizer->findFileByNumber($numero, $type, $langue);

    if (!empty($found_files)) {
      $this->output()->writeln("<fg=green>✓ Trouvé " . count($found_files) . " fichier(s):</fg=green>");

      foreach ($found_files as $file) {
        $this->output()->writeln("  📄 <comment>{$file['filename']}</comment>");
        $this->output()->writeln("     Langue: <info>{$file['info']['langue']}</info>");
        $this->output()->writeln("     Chemin: <comment>{$file['path']}</comment>");
        $this->output()->writeln("");
      }
    } else {
      $this->output()->writeln('<fg=red>✗ Aucun fichier trouvé</fg=red>');
    }
  }

  /**
   * Affiche la structure actuelle des dossiers PDF.
   *
   * @command pdf:structure
   * @aliases pdf-structure
   * @usage pdf:structure
   *   Affiche la structure des dossiers PDF.
   */
  #[CLI\Command(name: 'pdf:structure', aliases: ['pdf-structure'])]
  #[CLI\Usage(name: 'pdf:structure', description: 'Affiche la structure des dossiers PDF')]
  public function showStructure() {
    $this->output()->writeln("<fg=green>Structure des dossiers PDF:</fg=green>");

    $pdf_base_path = \Drupal::service('extension.list.module')->getPath('import_reglementation') . '/pdf';
    $this->output()->writeln("Chemin de base: <comment>$pdf_base_path</comment>\n");

    $this->displayDirectoryStructure($pdf_base_path, 0);
  }

  /**
   * Trouve le nœud de réglementation correspondant à un fichier PDF.
   *
   * @param string $filename
   *   Nom du fichier PDF à analyser.
   *
   * @command pdf:find-node
   * @aliases pdf-find-node
   * @usage pdf:find-node "Arrêté 20.80 Fr.pdf"
   *   Trouve le nœud correspondant au fichier PDF.
   */
  #[CLI\Command(name: 'pdf:find-node', aliases: ['pdf-find-node'])]
  #[CLI\Argument(name: 'filename', description: 'Nom du fichier PDF à analyser')]
  #[CLI\Usage(name: 'pdf:find-node "Arrêté 20.80 Fr.pdf"', description: 'Trouve le nœud correspondant au fichier PDF')]
  public function findNode($filename) {
    $this->output()->writeln("Recherche du nœud pour: <info>$filename</info>");

    // Analyser le fichier
    $file_info = $this->pdfOrganizer->analyzeFilename($filename);

    if (!$file_info['valid']) {
      $this->output()->writeln('<fg=red>✗ Impossible d\'analyser le nom de fichier</fg=red>');
      return;
    }

    $this->output()->writeln("Informations extraites:");
    $this->output()->writeln("  - Type: <comment>{$file_info['type']}</comment>");
    $this->output()->writeln("  - Numéro: <comment>{$file_info['numero']}</comment>");
    $this->output()->writeln("  - Langue: <comment>{$file_info['langue']}</comment>");

    // Chercher le nœud
    $node = $this->pdfOrganizer->findCorrespondingNode($file_info);

    if ($node) {
      $this->output()->writeln("<fg=green>✓ Nœud trouvé:</fg=green>");
      $this->output()->writeln("  - ID: <comment>{$node->id()}</comment>");
      $this->output()->writeln("  - Titre: <comment>{$node->label()}</comment>");
      $this->output()->writeln("  - Langue: <comment>{$node->language()->getId()}</comment>");
      $this->output()->writeln("  - URL: <comment>/node/{$node->id()}</comment>");
    } else {
      $this->output()->writeln('<fg=red>✗ Aucun nœud correspondant trouvé</fg=red>');
    }
  }

  /**
   * Affiche les informations de traduction d'un nœud trouvé via un fichier PDF.
   *
   * @param string $filename
   *   Nom du fichier PDF à analyser.
   *
   * @command pdf:translation-info
   * @aliases pdf-trans-info
   * @usage pdf:translation-info "Arrêté 20.80 Fr.pdf"
   *   Affiche les informations de traduction du nœud correspondant.
   */
  #[CLI\Command(name: 'pdf:translation-info', aliases: ['pdf-trans-info'])]
  #[CLI\Argument(name: 'filename', description: 'Nom du fichier PDF à analyser')]
  #[CLI\Usage(name: 'pdf:translation-info "Arrêté 20.80 Fr.pdf"', description: 'Affiche les informations de traduction du nœud correspondant')]
  public function translationInfo($filename) {
    $this->output()->writeln("Informations de traduction pour: <info>$filename</info>");

    // Analyser le fichier
    $file_info = $this->pdfOrganizer->analyzeFilename($filename);

    if (!$file_info['valid']) {
      $this->output()->writeln('<fg=red>✗ Impossible d\'analyser le nom de fichier</fg=red>');
      return;
    }

    // Chercher le nœud
    $node = $this->pdfOrganizer->findCorrespondingNode($file_info);

    if (!$node) {
      $this->output()->writeln('<fg=red>✗ Aucun nœud correspondant trouvé</fg=red>');
      return;
    }

    $this->output()->writeln("<fg=green>✓ Nœud trouvé: ID {$node->id()}</fg=green>");

    // Obtenir les informations de traduction
    $translation_info = $this->pdfOrganizer->getNodeTranslationInfo($node);

    $this->output()->writeln("\n<fg=yellow>Informations de traduction:</fg=yellow>");
    $this->output()->writeln("  Langue originale: <comment>{$translation_info['original_language']}</comment>");
    $this->output()->writeln("  Titre original: <comment>{$translation_info['original_title']}</comment>");

    $this->output()->writeln("\n<fg=yellow>Traductions disponibles:</fg=yellow>");

    foreach ($translation_info['translations'] as $langcode => $trans_info) {
      $status = $trans_info['exists'] ? '<fg=green>✓</fg=green>' : '<fg=red>✗</fg=red>';
      $language_name = $langcode === 'fr' ? 'Français' : 'Arabe';

      $this->output()->writeln("  $status <info>$language_name ($langcode)</info>");

      if ($trans_info['exists']) {
        $this->output()->writeln("     Titre: <comment>{$trans_info['title']}</comment>");
      } else {
        $this->output()->writeln("     <comment>Traduction non disponible</comment>");
      }
    }

    $this->output()->writeln("\n<fg=yellow>Résumé:</fg=yellow>");
    $this->output()->writeln("  - Français: " . ($translation_info['has_french'] ? '<fg=green>Disponible</fg=green>' : '<fg=red>Non disponible</fg=red>'));
    $this->output()->writeln("  - Arabe: " . ($translation_info['has_arabic'] ? '<fg=green>Disponible</fg=green>' : '<fg=red>Non disponible</fg=red>'));
  }

  /**
   * Traite un fichier PDF complet : trouve le nœud et attache le fichier.
   *
   * @param string $file_path
   *   Chemin vers le fichier PDF.
   *
   * @command pdf:process
   * @aliases pdf-process
   * @usage pdf:process "/path/to/Arrêté 20.80 Fr.pdf"
   *   Traite le fichier PDF et l'attache au nœud correspondant.
   */
  #[CLI\Command(name: 'pdf:process', aliases: ['pdf-process'])]
  #[CLI\Argument(name: 'file_path', description: 'Chemin vers le fichier PDF')]
  #[CLI\Usage(name: 'pdf:process "/path/to/Arrêté 20.80 Fr.pdf"', description: 'Traite le fichier PDF et l\'attache au nœud correspondant')]
  public function processFile($file_path) {
    $this->output()->writeln("Traitement du fichier: <info>$file_path</info>");

    if (!file_exists($file_path)) {
      $this->output()->writeln('<fg=red>✗ Le fichier n\'existe pas</fg=red>');
      return;
    }

    $result = $this->pdfOrganizer->processPdfFile($file_path);

    $this->output()->writeln("\n<fg=yellow>Résultats du traitement:</fg=yellow>");

    // Informations du fichier
    if (!empty($result['file_info'])) {
      $info = $result['file_info'];
      $this->output()->writeln("📄 <comment>Analyse du fichier:</comment>");
      $this->output()->writeln("   - Type: <info>{$info['type']}</info>");
      $this->output()->writeln("   - Numéro: <info>{$info['numero']}</info>");
      $this->output()->writeln("   - Langue: <info>{$info['langue']}</info>");
    }

    // Recherche du nœud
    if ($result['node_found']) {
      $this->output()->writeln("🔍 <fg=green>Nœud trouvé: ID {$result['node_id']}</fg=green>");
    } else {
      $this->output()->writeln("🔍 <fg=red>Aucun nœud correspondant trouvé</fg=red>");
    }

    // Attachement du fichier
    if ($result['file_attached']) {
      $this->output()->writeln("📎 <fg=green>Fichier attaché avec succès (File ID: {$result['file_id']})</fg=green>");
    }

    // Résultat final
    if ($result['success']) {
      $this->output()->writeln("\n<fg=green>✓ Traitement réussi</fg=green>");
      $this->output()->writeln("Message: <comment>{$result['message']}</comment>");
    } else {
      $this->output()->writeln("\n<fg=red>✗ Traitement échoué</fg=red>");
      $this->output()->writeln("Message: <comment>{$result['message']}</comment>");
    }
  }

  /**
   * Traite tous les fichiers PDF d'un dossier et les attache aux nœuds correspondants.
   *
   * @param string $source_directory
   *   Dossier contenant les fichiers PDF.
   * @param array $options
   *   Options de la commande.
   *
   * @command pdf:process-directory
   * @aliases pdf-process-dir
   * @option recursive Traiter les sous-dossiers récursivement
   * @usage pdf:process-directory /path/to/pdf/directory
   *   Traite tous les PDF du dossier.
   * @usage pdf:process-directory /path/to/pdf/directory --recursive
   *   Traite tous les PDF du dossier et sous-dossiers.
   */
  #[CLI\Command(name: 'pdf:process-directory', aliases: ['pdf-process-dir'])]
  #[CLI\Argument(name: 'source_directory', description: 'Dossier contenant les fichiers PDF')]
  #[CLI\Option(name: 'recursive', description: 'Traiter les sous-dossiers récursivement')]
  #[CLI\Usage(name: 'pdf:process-directory /path/to/pdf/directory', description: 'Traite tous les PDF du dossier')]
  #[CLI\Usage(name: 'pdf:process-directory /path/to/pdf/directory --recursive', description: 'Traite tous les PDF du dossier et sous-dossiers')]
  public function processDirectory($source_directory, array $options = ['recursive' => FALSE]) {
    $recursive = $options['recursive'];
    $recursion = $recursive ? 'récursif' : 'non-récursif';

    $this->output()->writeln("Traitement du dossier: <info>$source_directory</info> ($recursion)");

    if (!is_dir($source_directory)) {
      $this->output()->writeln('<fg=red>✗ Le dossier n\'existe pas</fg=red>');
      return;
    }

    $results = [
      'total' => 0,
      'success' => 0,
      'node_found' => 0,
      'file_attached' => 0,
      'errors' => 0,
      'details' => [],
    ];

    $iterator = $recursive ?
      new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($source_directory)) :
      new \DirectoryIterator($source_directory);

    foreach ($iterator as $file) {
      if ($file->isDot()) {
        continue;
      }

      if ($file->isFile() && preg_match('/\.pdf$/i', $file->getFilename())) {
        $results['total']++;
        $file_path = $file->getRealPath();

        $this->output()->writeln("\nTraitement: <comment>{$file->getFilename()}</comment>");

        $result = $this->pdfOrganizer->processPdfFile($file_path);

        if ($result['success']) {
          $results['success']++;
          $this->output()->writeln("  <fg=green>✓ Succès</fg=green>");
        } else {
          $results['errors']++;
          $this->output()->writeln("  <fg=red>✗ Erreur: {$result['message']}</fg=red>");
        }

        if ($result['node_found']) {
          $results['node_found']++;
        }

        if ($result['file_attached']) {
          $results['file_attached']++;
        }

        $results['details'][] = [
          'file' => $file->getFilename(),
          'result' => $result,
        ];
      }
    }

    $this->output()->writeln("\n<fg=green>Résultats finaux:</fg=green>");
    $this->output()->writeln("  Total de fichiers traités: <comment>{$results['total']}</comment>");
    $this->output()->writeln("  Succès: <comment>{$results['success']}</comment>");
    $this->output()->writeln("  Nœuds trouvés: <comment>{$results['node_found']}</comment>");
    $this->output()->writeln("  Fichiers attachés: <comment>{$results['file_attached']}</comment>");
    $this->output()->writeln("  Erreurs: <comment>{$results['errors']}</comment>");
  }

  /**
   * Affiche la structure d'un dossier de manière récursive.
   *
   * @param string $path
   *   Chemin du dossier.
   * @param int $level
   *   Niveau de profondeur.
   */
  protected function displayDirectoryStructure($path, $level = 0) {
    if (!is_dir($path)) {
      return;
    }

    $indent = str_repeat('  ', $level);
    $items = scandir($path);

    foreach ($items as $item) {
      if ($item === '.' || $item === '..') {
        continue;
      }

      $item_path = $path . '/' . $item;

      if (is_dir($item_path)) {
        $this->output()->writeln($indent . "📁 <info>$item/</info>");
        if ($level < 3) { // Limiter la profondeur
          $this->displayDirectoryStructure($item_path, $level + 1);
        }
      } else {
        $this->output()->writeln($indent . "📄 <comment>$item</comment>");
      }
    }
  }
}
