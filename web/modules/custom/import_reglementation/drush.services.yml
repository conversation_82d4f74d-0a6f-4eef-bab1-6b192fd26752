services:
  import_reglementation.commands:
    class: \Drush\Commands\import_reglementation\ImportReglementationCommands
    arguments: ['@import_reglementation.csv_importer']
    tags:
      - { name: drush.command }

  import_reglementation.commands_rh:
    class: \Drush\Commands\import_reglementation\ImportReglementationRHCommands
    arguments: ['@import_reglementation.csv_importer_rh']
    tags:
      - { name: drush.command }

  import_reglementation.pdf_organizer_commands:
    class: \Drush\Commands\import_reglementation\PdfOrganizerCommandsSimple
    arguments: ['@import_reglementation.pdf_organizer']
    tags:
      - { name: drush.command }
