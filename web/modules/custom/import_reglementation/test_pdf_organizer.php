<?php

/**
 * Script de test pour le service PdfOrganizer.
 * 
 * Ce script démontre comment utiliser le nouveau service PdfOrganizer
 * pour analyser et organiser les fichiers PDF selon la structure :
 * pdf/[Langue]/[Type de réglementation]/[Nom du fichier]
 * 
 * Usage: php test_pdf_organizer.php
 */

use Drupal\Core\DrupalKernel;
use Symfony\Component\HttpFoundation\Request;

// Charger Drupal
$autoloader = require_once 'autoload.php';
$kernel = new DrupalKernel('prod', $autoloader);
$request = Request::createFromGlobals();
$response = $kernel->handle($request);
$kernel->boot();

// Obtenir le service PdfOrganizer
$pdf_organizer = \Drupal::service('import_reglementation.pdf_organizer');

echo "=== Test du service PdfOrganizer ===\n\n";

// Test 1: Analyser différents noms de fichiers
echo "1. Test d'analyse de noms de fichiers:\n";
echo "=====================================\n";

$test_filenames = [
  'Arrêté 20.80 Fr.pdf',
  'Loi 116.14 Ar.pdf',
  'Décret 2.10.311 Fr.pdf',
  'Dahir 1.72.093 AR.pdf',
  'Circulaire 123.45 Fr.pdf',
  'CDC_Transport_Fr.pdf',
  'Arrete 2445.96 Fr.pdf',  // Sans accent
  'Loi_52.05_Ar.pdf',       // Avec underscores
  'fichier_invalide.pdf',   // Nom invalide
];

foreach ($test_filenames as $filename) {
  echo "\nAnalyse de: $filename\n";
  $result = $pdf_organizer->analyzeFilename($filename);
  
  if ($result['valid']) {
    echo "  ✓ Valide\n";
    echo "  - Type: {$result['type']}\n";
    echo "  - Numéro: {$result['numero']}\n";
    echo "  - Langue: {$result['langue']}\n";
    
    // Générer le chemin de destination
    $destination_path = $pdf_organizer->generateDestinationPath($result);
    echo "  - Chemin de destination: $destination_path\n";
  } else {
    echo "  ✗ Invalide - Impossible d'analyser le nom de fichier\n";
  }
}

// Test 2: Rechercher des fichiers existants
echo "\n\n2. Test de recherche de fichiers existants:\n";
echo "==========================================\n";

$search_tests = [
  ['numero' => '20.80', 'type' => 'Arrêté'],
  ['numero' => '116.14', 'type' => 'Loi'],
  ['numero' => '2.10.311', 'type' => 'Décret'],
];

foreach ($search_tests as $search) {
  echo "\nRecherche: {$search['type']} {$search['numero']}\n";
  $found_files = $pdf_organizer->findFileByNumber($search['numero'], $search['type']);
  
  if (!empty($found_files)) {
    echo "  ✓ Trouvé " . count($found_files) . " fichier(s):\n";
    foreach ($found_files as $file) {
      echo "    - {$file['filename']} ({$file['info']['langue']})\n";
      echo "      Chemin: {$file['path']}\n";
    }
  } else {
    echo "  ✗ Aucun fichier trouvé\n";
  }
}

// Test 3: Simulation d'organisation de fichiers (sans déplacer réellement)
echo "\n\n3. Test de simulation d'organisation:\n";
echo "====================================\n";

// Créer un fichier de test temporaire
$temp_dir = sys_get_temp_dir();
$test_file_path = $temp_dir . '/Arrêté_Test_123.45_Fr.pdf';

// Créer un fichier PDF factice pour le test
file_put_contents($test_file_path, '%PDF-1.4 Test PDF content');

echo "Fichier de test créé: $test_file_path\n";

// Analyser le fichier de test
$result = $pdf_organizer->organizeFile($test_file_path, false); // false = copier, pas déplacer

echo "\nRésultat de l'organisation:\n";
if ($result['success']) {
  echo "  ✓ Succès\n";
  echo "  - Message: {$result['message']}\n";
  echo "  - Destination: {$result['destination_path']}\n";
  echo "  - Type détecté: {$result['file_info']['type']}\n";
  echo "  - Numéro détecté: {$result['file_info']['numero']}\n";
  echo "  - Langue détectée: {$result['file_info']['langue']}\n";
} else {
  echo "  ✗ Échec\n";
  echo "  - Message: {$result['message']}\n";
}

// Nettoyer le fichier de test
if (file_exists($test_file_path)) {
  unlink($test_file_path);
  echo "\nFichier de test supprimé.\n";
}

// Test 4: Afficher la structure actuelle des dossiers PDF
echo "\n\n4. Structure actuelle des dossiers PDF:\n";
echo "======================================\n";

$pdf_base_path = \Drupal::service('extension.list.module')->getPath('import_reglementation') . '/pdf';

function displayDirectoryStructure($path, $level = 0) {
  if (!is_dir($path)) {
    return;
  }
  
  $indent = str_repeat('  ', $level);
  $items = scandir($path);
  
  foreach ($items as $item) {
    if ($item === '.' || $item === '..') {
      continue;
    }
    
    $item_path = $path . '/' . $item;
    
    if (is_dir($item_path)) {
      echo $indent . "📁 $item/\n";
      if ($level < 2) { // Limiter la profondeur pour éviter trop de sortie
        displayDirectoryStructure($item_path, $level + 1);
      }
    } else {
      echo $indent . "📄 $item\n";
    }
  }
}

echo "Chemin de base: $pdf_base_path\n\n";
displayDirectoryStructure($pdf_base_path);

echo "\n\n=== Fin des tests ===\n";

// Afficher des exemples d'utilisation
echo "\n\nExemples d'utilisation du service:\n";
echo "==================================\n";

echo "
// Obtenir le service
\$pdf_organizer = \\Drupal::service('import_reglementation.pdf_organizer');

// Analyser un nom de fichier
\$result = \$pdf_organizer->analyzeFilename('Arrêté 20.80 Fr.pdf');
if (\$result['valid']) {
  echo \"Type: {\$result['type']}, Numéro: {\$result['numero']}, Langue: {\$result['langue']}\";
}

// Organiser un fichier (copier)
\$result = \$pdf_organizer->organizeFile('/path/to/file.pdf', false);

// Organiser un fichier (déplacer)
\$result = \$pdf_organizer->organizeFile('/path/to/file.pdf', true);

// Organiser tout un dossier
\$results = \$pdf_organizer->organizeDirectory('/path/to/source/directory', false);

// Rechercher un fichier par numéro et type
\$files = \$pdf_organizer->findFileByNumber('20.80', 'Arrêté', 'Fr');
";

echo "\n";
