{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "2cbc4ab26d109382fd27c8f0b97bb909", "packages": [{"name": "asm89/stack-cors", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/asm89/stack-cors.git", "reference": "50f57105bad3d97a43ec4a485eb57daf347eafea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/asm89/stack-cors/zipball/50f57105bad3d97a43ec4a485eb57daf347eafea", "reference": "50f57105bad3d97a43ec4a485eb57daf347eafea", "shasum": ""}, "require": {"php": "^7.3|^8.0", "symfony/http-foundation": "^5.3|^6|^7", "symfony/http-kernel": "^5.3|^6|^7"}, "require-dev": {"phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"Asm89\\Stack\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Cross-origin resource sharing library and stack middleware", "homepage": "https://github.com/asm89/stack-cors", "keywords": ["cors", "stack"], "support": {"issues": "https://github.com/asm89/stack-cors/issues", "source": "https://github.com/asm89/stack-cors/tree/v2.2.0"}, "time": "2023-11-14T13:51:46+00:00"}, {"name": "chi-teck/drupal-code-generator", "version": "4.1.0", "source": {"type": "git", "url": "https://github.com/Chi-teck/drupal-code-generator.git", "reference": "9a5501beb1a7aa2400afa5e5679bf21c526c497c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Chi-teck/drupal-code-generator/zipball/9a5501beb1a7aa2400afa5e5679bf21c526c497c", "reference": "9a5501beb1a7aa2400afa5e5679bf21c526c497c", "shasum": ""}, "require": {"ext-json": "*", "php": ">=8.3.0", "psr/event-dispatcher": "^1.0", "psr/log": "^3.0", "symfony/console": "^7.1", "symfony/dependency-injection": "^7.1", "symfony/filesystem": "^7.1", "symfony/string": "^7.0", "twig/twig": "^3.4"}, "conflict": {"nikic/php-parser": "<4.17", "squizlabs/php_codesniffer": "<3.6"}, "require-dev": {"chi-teck/drupal-coder-extension": "^2.0.0-beta3", "drupal/coder": "8.3.24", "drupal/core": "11.x-dev", "ext-simplexml": "*", "phpspec/prophecy-phpunit": "^2.2", "phpunit/phpunit": "^10.5", "squizlabs/php_codesniffer": "^3.9", "symfony/var-dumper": "^7.1", "symfony/yaml": "^7.1", "vimeo/psalm": "^5.24.0"}, "bin": ["bin/dcg"], "type": "library", "autoload": {"psr-4": {"DrupalCodeGenerator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Drupal code generator", "support": {"issues": "https://github.com/Chi-teck/drupal-code-generator/issues", "source": "https://github.com/Chi-teck/drupal-code-generator/tree/4.1.0"}, "time": "2024-10-30T18:25:43+00:00"}, {"name": "composer/installers", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "12fb2dfe5e16183de69e784a7b84046c43d97e8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/12fb2dfe5e16183de69e784a7b84046c43d97e8e", "reference": "12fb2dfe5e16183de69e784a7b84046c43d97e8e", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": "^7.2 || ^8.0"}, "require-dev": {"composer/composer": "^1.10.27 || ^2.7", "composer/semver": "^1.7.2 || ^3.4.0", "phpstan/phpstan": "^1.11", "phpstan/phpstan-phpunit": "^1", "symfony/phpunit-bridge": "^7.1.1", "symfony/process": "^5 || ^6 || ^7"}, "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "2.x-dev"}, "plugin-modifies-install-path": true}, "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Starbug", "Thelia", "Whmcs", "WolfCMS", "agl", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "concreteCMS", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "matomo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "pantheon", "phpbb", "piwik", "ppi", "processwire", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "tastyigniter", "wordpress", "yawik", "zend", "zikula"], "support": {"issues": "https://github.com/composer/installers/issues", "source": "https://github.com/composer/installers/tree/v2.3.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-06-24T20:46:46+00:00"}, {"name": "composer/pcre", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.10"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-strict-rules": "^1 || ^2", "phpunit/phpunit": "^8 || ^9"}, "type": "library", "extra": {"phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.3.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-11-12T16:29:46+00:00"}, {"name": "composer/semver", "version": "3.4.3", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-09-19T14:15:21+00:00"}, {"name": "consolidation/annotated-command", "version": "4.10.1", "source": {"type": "git", "url": "https://github.com/consolidation/annotated-command.git", "reference": "362310b13ececa9f6f0a4a880811fa08fecc348b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/annotated-command/zipball/362310b13ececa9f6f0a4a880811fa08fecc348b", "reference": "362310b13ececa9f6f0a4a880811fa08fecc348b", "shasum": ""}, "require": {"consolidation/output-formatters": "^4.3.1", "php": ">=7.1.3", "psr/log": "^1 || ^2 || ^3", "symfony/console": "^4.4.8 || ^5 || ^6 || ^7", "symfony/event-dispatcher": "^4.4.8 || ^5 || ^6 || ^7", "symfony/finder": "^4.4.8 || ^5 || ^6 || ^7"}, "require-dev": {"composer-runtime-api": "^2.0", "phpunit/phpunit": "^7.5.20 || ^8 || ^9", "squizlabs/php_codesniffer": "^3", "yoast/phpunit-polyfills": "^0.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\AnnotatedCommand\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Initialize Symfony Console commands from annotated command class methods.", "support": {"issues": "https://github.com/consolidation/annotated-command/issues", "source": "https://github.com/consolidation/annotated-command/tree/4.10.1"}, "time": "2024-12-13T19:55:40+00:00"}, {"name": "consolidation/config", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/consolidation/config.git", "reference": "0615499781449ab773ffc609b97b934b3357b3f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/config/zipball/0615499781449ab773ffc609b97b934b3357b3f9", "reference": "0615499781449ab773ffc609b97b934b3357b3f9", "shasum": ""}, "require": {"dflydev/dot-access-data": "^3", "grasmash/expander": "^3", "php": ">=8.2.0", "symfony/event-dispatcher": "^7"}, "require-dev": {"ext-json": "*", "phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3", "symfony/console": "^7", "symfony/yaml": "^7", "yoast/phpunit-polyfills": "^1"}, "suggest": {"symfony/event-dispatcher": "Required to inject configuration into Command options", "symfony/yaml": "Required to use Consolidation\\Config\\Loader\\YamlConfigLoader"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\Config\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Provide configuration services for a commandline tool.", "support": {"issues": "https://github.com/consolidation/config/issues", "source": "https://github.com/consolidation/config/tree/3.1.0"}, "time": "2024-11-28T14:37:27+00:00"}, {"name": "consolidation/filter-via-dot-access-data", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/consolidation/filter-via-dot-access-data.git", "reference": "cb2eeba41f8e2e3c61698a5cf70ef048ff6c9d5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/filter-via-dot-access-data/zipball/cb2eeba41f8e2e3c61698a5cf70ef048ff6c9d5b", "reference": "cb2eeba41f8e2e3c61698a5cf70ef048ff6c9d5b", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0 || ^2.0.0 || ^3.0.0", "php": ">=7.1.3"}, "require-dev": {"phpunit/phpunit": "^7.5.20 || ^8 || ^9", "squizlabs/php_codesniffer": "^3", "yoast/phpunit-polyfills": "^0.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\Filter\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "This project uses dflydev/dot-access-data to provide simple output filtering for applications built with annotated-command / Robo.", "support": {"source": "https://github.com/consolidation/filter-via-dot-access-data/tree/2.0.2"}, "time": "2021-12-30T03:56:08+00:00"}, {"name": "consolidation/log", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/consolidation/log.git", "reference": "c27a3beb36137c141ccbce0d89f64befb243c015"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/log/zipball/c27a3beb36137c141ccbce0d89f64befb243c015", "reference": "c27a3beb36137c141ccbce0d89f64befb243c015", "shasum": ""}, "require": {"php": ">=8.0.0", "psr/log": "^3", "symfony/console": "^5 || ^6 || ^7"}, "require-dev": {"phpunit/phpunit": "^7.5.20 || ^8 || ^9", "squizlabs/php_codesniffer": "^3", "yoast/phpunit-polyfills": "^0.2.0"}, "type": "library", "extra": {"platform": {"php": "8.2.17"}}, "autoload": {"psr-4": {"Consolidation\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Improved Psr-3 / Psr\\Log logger based on Symfony Console components.", "support": {"issues": "https://github.com/consolidation/log/issues", "source": "https://github.com/consolidation/log/tree/3.1.0"}, "time": "2024-04-04T23:50:25+00:00"}, {"name": "consolidation/output-formatters", "version": "4.6.0", "source": {"type": "git", "url": "https://github.com/consolidation/output-formatters.git", "reference": "5fd5656718d7068a02d046f418a7ba873d5abbfe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/output-formatters/zipball/5fd5656718d7068a02d046f418a7ba873d5abbfe", "reference": "5fd5656718d7068a02d046f418a7ba873d5abbfe", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0 || ^2 || ^3", "php": ">=7.1.3", "symfony/console": "^4 || ^5 || ^6 || ^7", "symfony/finder": "^4 || ^5 || ^6 || ^7"}, "require-dev": {"php-coveralls/php-coveralls": "^2.4.2", "phpunit/phpunit": "^7 || ^8 || ^9", "squizlabs/php_codesniffer": "^3", "symfony/var-dumper": "^4 || ^5 || ^6 || ^7", "symfony/yaml": "^4 || ^5 || ^6 || ^7", "yoast/phpunit-polyfills": "^1"}, "suggest": {"symfony/var-dumper": "For using the var_dump formatter"}, "type": "library", "autoload": {"psr-4": {"Consolidation\\OutputFormatters\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Format text by applying transformations provided by plug-in formatters.", "support": {"issues": "https://github.com/consolidation/output-formatters/issues", "source": "https://github.com/consolidation/output-formatters/tree/4.6.0"}, "time": "2024-10-18T14:02:48+00:00"}, {"name": "consolidation/robo", "version": "5.1.0", "source": {"type": "git", "url": "https://github.com/consolidation/robo.git", "reference": "dde6bd88de5e1e8a7f6ed8906f80353817647ad9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/robo/zipball/dde6bd88de5e1e8a7f6ed8906f80353817647ad9", "reference": "dde6bd88de5e1e8a7f6ed8906f80353817647ad9", "shasum": ""}, "require": {"consolidation/annotated-command": "^4.8.1", "consolidation/config": "^3", "consolidation/log": "^3", "consolidation/output-formatters": "^4.1.2", "league/container": "^3.3.1 || ^4.0", "php": ">=8.2", "phpowermove/docblock": "^4.0", "symfony/console": "^6 || ^7", "symfony/event-dispatcher": "^6 || ^7", "symfony/filesystem": "^6 || ^7", "symfony/finder": "^6 || ^7", "symfony/process": "^6 || ^7", "symfony/yaml": "^6 || ^7"}, "conflict": {"codegyre/robo": "*"}, "require-dev": {"natxet/cssmin": "3.0.4", "patchwork/jsqueeze": "^2", "pear/archive_tar": "^1.4.4", "phpunit/phpunit": "^7.5.20 || ^8 || ^9", "squizlabs/php_codesniffer": "^3.6", "yoast/phpunit-polyfills": "^0.2.0"}, "suggest": {"consolidation/self-update": "For self-updating a phar-based app built with Robo", "natxet/cssmin": "For minifying CSS files in taskMinify", "patchwork/jsqueeze": "For minifying JS files in taskMinify", "pear/archive_tar": "Allows tar archives to be created and extracted in taskPack and taskExtract, respectively.", "totten/lurkerlite": "For monitoring filesystem changes in taskWatch"}, "bin": ["robo"], "type": "library", "autoload": {"psr-4": {"Robo\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Modern task runner", "support": {"issues": "https://github.com/consolidation/robo/issues", "source": "https://github.com/consolidation/robo/tree/5.1.0"}, "time": "2024-10-22T13:18:54+00:00"}, {"name": "consolidation/site-alias", "version": "4.1.1", "source": {"type": "git", "url": "https://github.com/consolidation/site-alias.git", "reference": "aff6189aae17da813d23249cb2fc0fff33f26d40"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/site-alias/zipball/aff6189aae17da813d23249cb2fc0fff33f26d40", "reference": "aff6189aae17da813d23249cb2fc0fff33f26d40", "shasum": ""}, "require": {"consolidation/config": "^1.2.1 || ^2 || ^3", "php": ">=7.4", "symfony/filesystem": "^5.4 || ^6 || ^7", "symfony/finder": "^5 || ^6 || ^7"}, "require-dev": {"php-coveralls/php-coveralls": "^2.4.2", "phpunit/phpunit": ">=7", "squizlabs/php_codesniffer": "^3", "symfony/var-dumper": "^4", "yoast/phpunit-polyfills": "^0.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\SiteAlias\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Manage alias records for local and remote sites.", "support": {"issues": "https://github.com/consolidation/site-alias/issues", "source": "https://github.com/consolidation/site-alias/tree/4.1.1"}, "time": "2024-12-13T19:05:11+00:00"}, {"name": "consolidation/site-process", "version": "5.4.2", "source": {"type": "git", "url": "https://github.com/consolidation/site-process.git", "reference": "e7fafc40ebfddc1a5ee99ee66e5d186fc1bed4da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/site-process/zipball/e7fafc40ebfddc1a5ee99ee66e5d186fc1bed4da", "reference": "e7fafc40ebfddc1a5ee99ee66e5d186fc1bed4da", "shasum": ""}, "require": {"consolidation/config": "^2 || ^3", "consolidation/site-alias": "^3 || ^4", "php": ">=8.0.14", "symfony/console": "^5.4 || ^6 || ^7", "symfony/process": "^6 || ^7"}, "require-dev": {"phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\SiteProcess\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A thin wrapper around the Symfony Process Component that allows applications to use the Site Alias library to specify the target for a remote call.", "support": {"issues": "https://github.com/consolidation/site-process/issues", "source": "https://github.com/consolidation/site-process/tree/5.4.2"}, "time": "2024-12-13T19:25:56+00:00"}, {"name": "dekor/php-array-table", "version": "2.0", "source": {"type": "git", "url": "https://github.com/deniskoronets/php-array-table.git", "reference": "ca40b21ba84eee6a9658a33fc5f897d76baaf8e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/deniskoronets/php-array-table/zipball/ca40b21ba84eee6a9658a33fc5f897d76baaf8e5", "reference": "ca40b21ba84eee6a9658a33fc5f897d76baaf8e5", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "^10"}, "type": "library", "autoload": {"psr-4": {"dekor\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://woo.zp.ua/"}], "description": "PHP Library for printing associative arrays as text table (similar to mysql terminal console)", "keywords": ["library", "php"], "support": {"issues": "https://github.com/deniskoronets/php-array-table/issues", "source": "https://github.com/deniskoronets/php-array-table/tree/2.0"}, "time": "2023-02-10T10:13:42+00:00"}, {"name": "dflydev/dot-access-data", "version": "v3.0.3", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-dot-access-data.git", "reference": "a23a2bf4f31d3518f3ecb38660c95715dfead60f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-dot-access-data/zipball/a23a2bf4f31d3518f3ecb38660c95715dfead60f", "reference": "a23a2bf4f31d3518f3ecb38660c95715dfead60f", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.42", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.3", "scrutinizer/ocular": "1.6.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Dflydev\\DotAccessData\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/cfrutos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com"}], "description": "Given a deep data structure, access data by dot notation.", "homepage": "https://github.com/dflydev/dflydev-dot-access-data", "keywords": ["access", "data", "dot", "notation"], "support": {"issues": "https://github.com/dflydev/dflydev-dot-access-data/issues", "source": "https://github.com/dflydev/dflydev-dot-access-data/tree/v3.0.3"}, "time": "2024-07-08T12:26:09+00:00"}, {"name": "doctrine/annotations", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "901c2ee5d26eb64ff43c47976e114bf00843acf7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/901c2ee5d26eb64ff43c47976e114bf00843acf7", "reference": "901c2ee5d26eb64ff43c47976e114bf00843acf7", "shasum": ""}, "require": {"doctrine/lexer": "^2 || ^3", "ext-tokenizer": "*", "php": "^7.2 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^2.0", "doctrine/coding-standard": "^10", "phpstan/phpstan": "^1.10.28", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^5.4 || ^6.4 || ^7", "vimeo/psalm": "^4.30 || ^5.14"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/2.0.2"}, "time": "2024-09-05T10:17:24+00:00"}, {"name": "doctrine/common", "version": "3.5.0", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "d9ea4a54ca2586db781f0265d36bea731ac66ec5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/d9ea4a54ca2586db781f0265d36bea731ac66ec5", "reference": "d9ea4a54ca2586db781f0265d36bea731ac66ec5", "shasum": ""}, "require": {"doctrine/persistence": "^2.0 || ^3.0 || ^4.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0 || ^10.0", "doctrine/collections": "^1", "phpstan/phpstan": "^1.4.1", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5.20 || ^8.5 || ^9.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^6.1", "vimeo/psalm": "^4.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, proxies and much more.", "homepage": "https://www.doctrine-project.org/projects/common.html", "keywords": ["common", "doctrine", "php"], "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.5.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcommon", "type": "tidelift"}], "time": "2025-01-01T22:12:03+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "time": "2025-04-07T20:06:18+00:00"}, {"name": "doctrine/event-manager", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/b680156fa328f1dfd874fd48c7026c41570b9c6e", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^10.5", "vimeo/psalm": "^5.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/2.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2024-05-22T20:47:39+00:00"}, {"name": "doctrine/lexer", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/2.1.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:35:39+00:00"}, {"name": "doctrine/persistence", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "45004aca79189474f113cbe3a53847c2115a55fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/45004aca79189474f113cbe3a53847c2115a55fa", "reference": "45004aca79189474f113cbe3a53847c2115a55fa", "shasum": ""}, "require": {"doctrine/event-manager": "^1 || ^2", "php": "^8.1", "psr/cache": "^1.0 || ^2.0 || ^3.0"}, "conflict": {"doctrine/common": "<2.10"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "1.12.7", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^9.6", "symfony/cache": "^4.4 || ^5.4 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Persistence\\": "src/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://www.doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/4.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fpersistence", "type": "tidelift"}], "time": "2024-11-01T21:49:07+00:00"}, {"name": "drupal/addtoany", "version": "2.0.7", "source": {"type": "git", "url": "https://git.drupalcode.org/project/addtoany.git", "reference": "2.0.7"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/addtoany-2.0.7.zip", "reference": "2.0.7", "shasum": "33bfb5f425134705f8208f7c2f6ac23382a87bb6"}, "require": {"drupal/core": "^10.1 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.7", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "AddToAny", "homepage": "https://www.drupal.org/user/2640913"}, {"name": "micropat", "homepage": "https://www.drupal.org/user/260224"}], "description": "Share buttons for Drupal including the AddToAny universal share button, Facebook, Mastodon, Pinterest, WhatsApp and many more.", "homepage": "https://www.drupal.org/project/addtoany", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "https://git.drupalcode.org/project/addtoany", "issues": "https://www.drupal.org/project/issues/addtoany"}}, {"name": "drupal/admin_toolbar", "version": "3.6.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/admin_toolbar.git", "reference": "3.6.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/admin_toolbar-3.6.0.zip", "reference": "3.6.0", "shasum": "ee03fd8e5394525f6e0aaf19db75624fbefc5b5d"}, "require": {"drupal/core": "^9.5 || ^10 || ^11"}, "require-dev": {"drupal/admin_toolbar_tools": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.6.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON> (eme)", "homepage": "https://www.drupal.org/u/eme", "role": "Maintainer"}, {"name": "<PERSON><PERSON> (romainj)", "homepage": "https://www.drupal.org/u/romainj", "role": "Maintainer"}, {"name": "<PERSON> (adriancid)", "homepage": "https://www.drupal.org/u/adriancid", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON> (matio89)", "homepage": "https://www.drupal.org/u/matio89", "role": "Maintainer"}, {"name": "fethi.krout", "homepage": "https://www.drupal.org/user/3206765"}, {"name": "japerry", "homepage": "https://www.drupal.org/user/45640"}, {"name": "matio89", "homepage": "https://www.drupal.org/user/2320090"}, {"name": "musa.thomas", "homepage": "https://www.drupal.org/user/1213824"}, {"name": "r<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/370706"}], "description": "Provides a drop-down menu interface to the core Drupal Toolbar.", "homepage": "http://drupal.org/project/admin_toolbar", "keywords": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "support": {"source": "https://git.drupalcode.org/project/admin_toolbar", "issues": "https://www.drupal.org/project/issues/admin_toolbar"}}, {"name": "drupal/better_exposed_filters", "version": "7.0.5", "source": {"type": "git", "url": "https://git.drupalcode.org/project/better_exposed_filters.git", "reference": "7.0.5"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/better_exposed_filters-7.0.5.zip", "reference": "7.0.5", "shasum": "a215444c39a6ae384710a6c707caf593f6dd1e2d"}, "require": {"drupal/core": "^10 || ^11", "drupal/nouislider_js": "^15.8"}, "type": "drupal-module", "extra": {"drupal": {"version": "7.0.5", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/mikeker"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/etroid"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/u/neslee-canil-pinto"}, {"name": "mikeker", "homepage": "https://www.drupal.org/user/192273"}, {"name": "neslee canil pinto", "homepage": "https://www.drupal.org/user/3580850"}, {"name": "podarok", "homepage": "https://www.drupal.org/user/116002"}, {"name": "rlhawk", "homepage": "https://www.drupal.org/user/352283"}, {"name": "s<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3252890"}], "description": "Replaces the Views default single- or multi-select boxes with more advanced options.", "homepage": "https://www.drupal.org/project/better_exposed_filters", "support": {"source": "https://git.drupalcode.org/project/better_exposed_filters", "issues": "https://www.drupal.org/project/issues/better_exposed_filters"}}, {"name": "drupal/captcha", "version": "2.0.7", "source": {"type": "git", "url": "https://git.drupalcode.org/project/captcha.git", "reference": "2.0.7"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/captcha-2.0.7.zip", "reference": "2.0.7", "shasum": "8e97ba41810811bcd5d7e8b714cdc0b664dd8eec"}, "require": {"drupal/core": "^9.5 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.7", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "branch-alias": {"dev-8.x-1.x": "1.x-dev"}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "anybody", "homepage": "https://www.drupal.org/user/291091"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1021502"}, {"name": "grevil", "homepage": "https://www.drupal.org/user/3668491"}, {"name": "japerry", "homepage": "https://www.drupal.org/user/45640"}, {"name": "naveenvalecha", "homepage": "https://www.drupal.org/user/2665733"}, {"name": "podarok", "homepage": "https://www.drupal.org/user/116002"}, {"name": "robloach", "homepage": "https://www.drupal.org/user/61114"}, {"name": "thomas.fro<PERSON>ter", "homepage": "https://www.drupal.org/user/409335"}, {"name": "wundo", "homepage": "https://www.drupal.org/user/25523"}], "description": "The CAPTCHA module provides this feature to virtually any user facing web form on a Drupal site.", "homepage": "https://www.drupal.org/project/captcha", "support": {"source": "https://git.drupalcode.org/project/captcha", "issues": "https://www.drupal.org/project/issues/captcha"}}, {"name": "drupal/config_pages", "version": "2.17.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/config_pages.git", "reference": "8.x-2.17"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/config_pages-8.x-2.17.zip", "reference": "8.x-2.17", "shasum": "717b1eb911e30e766a891d45eeefa34825794ac4"}, "require": {"drupal/core": "^8.5 | ^9 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.17", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3193903"}, {"name": "qwaygon", "homepage": "https://www.drupal.org/user/636624"}, {"name": "shumer", "homepage": "https://www.drupal.org/user/2297432"}], "description": "ConfigPages module", "homepage": "http://drupal.org/project/config_pages", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "http://cgit.drupalcode.org/config_pages", "issues": "http://drupal.org/project/issues/config_pages"}}, {"name": "drupal/core", "version": "11.1.7", "source": {"type": "git", "url": "https://github.com/drupal/core.git", "reference": "b0241948975ede8ca221971ecd3f71793447fded"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core/zipball/b0241948975ede8ca221971ecd3f71793447fded", "reference": "b0241948975ede8ca221971ecd3f71793447fded", "shasum": ""}, "require": {"asm89/stack-cors": "^2.1", "composer-runtime-api": "^2.1", "composer/semver": "^3.3", "doctrine/annotations": "^2.0", "doctrine/lexer": "^2.0", "egulias/email-validator": "^3.2.1|^4.0", "ext-date": "*", "ext-dom": "*", "ext-filter": "*", "ext-gd": "*", "ext-hash": "*", "ext-json": "*", "ext-pcre": "*", "ext-pdo": "*", "ext-session": "*", "ext-simplexml": "*", "ext-spl": "*", "ext-tokenizer": "*", "ext-xml": "*", "ext-zlib": "*", "guzzlehttp/guzzle": "^7.5", "guzzlehttp/psr7": "^2.4.5", "masterminds/html5": "^2.7", "mck89/peast": "^1.14", "pear/archive_tar": "^1.4.14", "php": ">=8.3.0", "php-tuf/composer-stager": "^2-rc5", "psr/log": "^3.0", "revolt/event-loop": "^1.0", "sebastian/diff": "^4|^5", "symfony/console": "^7.2", "symfony/dependency-injection": "^7.2", "symfony/event-dispatcher": "^7.2", "symfony/filesystem": "^7.2", "symfony/finder": "^7.2", "symfony/http-foundation": "^7.2", "symfony/http-kernel": "^7.2", "symfony/mailer": "^7.2", "symfony/mime": "^7.2", "symfony/polyfill-iconv": "^1.26", "symfony/process": "^7.2", "symfony/psr-http-message-bridge": "^7.2", "symfony/routing": "^7.2", "symfony/serializer": "^7.2", "symfony/validator": "^7.2", "symfony/yaml": "^7.2", "twig/twig": "^3.15.0"}, "conflict": {"drush/drush": "<12.4.3"}, "replace": {"drupal/core-annotation": "self.version", "drupal/core-assertion": "self.version", "drupal/core-class-finder": "self.version", "drupal/core-datetime": "self.version", "drupal/core-dependency-injection": "self.version", "drupal/core-diff": "self.version", "drupal/core-discovery": "self.version", "drupal/core-event-dispatcher": "self.version", "drupal/core-file-cache": "self.version", "drupal/core-file-security": "self.version", "drupal/core-filesystem": "self.version", "drupal/core-front-matter": "self.version", "drupal/core-gettext": "self.version", "drupal/core-graph": "self.version", "drupal/core-http-foundation": "self.version", "drupal/core-php-storage": "self.version", "drupal/core-plugin": "self.version", "drupal/core-proxy-builder": "self.version", "drupal/core-render": "self.version", "drupal/core-serialization": "self.version", "drupal/core-transliteration": "self.version", "drupal/core-utility": "self.version", "drupal/core-uuid": "self.version", "drupal/core-version": "self.version"}, "suggest": {"ext-zip": "Needed to extend the plugin.manager.archiver service capability with the handling of files in the ZIP format."}, "type": "drupal-core", "extra": {"drupal-scaffold": {"file-mapping": {"[web-root]/.htaccess": "assets/scaffold/files/htaccess", "[web-root]/README.md": "assets/scaffold/files/drupal.README.md", "[web-root]/index.php": "assets/scaffold/files/index.php", "[web-root]/.csslintrc": "assets/scaffold/files/csslintrc", "[web-root]/robots.txt": "assets/scaffold/files/robots.txt", "[web-root]/update.php": "assets/scaffold/files/update.php", "[web-root]/INSTALL.txt": "assets/scaffold/files/drupal.INSTALL.txt", "[web-root]/.eslintignore": "assets/scaffold/files/eslintignore", "[web-root]/.eslintrc.json": "assets/scaffold/files/eslintrc.json", "[web-root]/.ht.router.php": "assets/scaffold/files/ht.router.php", "[web-root]/sites/README.txt": "assets/scaffold/files/sites.README.txt", "[project-root]/.editorconfig": "assets/scaffold/files/editorconfig", "[web-root]/example.gitignore": "assets/scaffold/files/example.gitignore", "[web-root]/themes/README.txt": "assets/scaffold/files/themes.README.txt", "[project-root]/.gitattributes": "assets/scaffold/files/gitattributes", "[web-root]/modules/README.txt": "assets/scaffold/files/modules.README.txt", "[web-root]/profiles/README.txt": "assets/scaffold/files/profiles.README.txt", "[project-root]/recipes/README.txt": "assets/scaffold/files/recipes.README.txt", "[web-root]/sites/example.sites.php": "assets/scaffold/files/example.sites.php", "[web-root]/sites/development.services.yml": "assets/scaffold/files/development.services.yml", "[web-root]/sites/example.settings.local.php": "assets/scaffold/files/example.settings.local.php", "[web-root]/sites/default/default.services.yml": "assets/scaffold/files/default.services.yml", "[web-root]/sites/default/default.settings.php": "assets/scaffold/files/default.settings.php"}}}, "autoload": {"files": ["includes/bootstrap.inc"], "psr-4": {"Drupal\\Core\\": "lib/Drupal/Core", "Drupal\\Component\\": "lib/Drupal/Component"}, "classmap": ["lib/Drupal.php", "lib/Drupal/Component/DependencyInjection/Container.php", "lib/Drupal/Component/DependencyInjection/PhpArrayContainer.php", "lib/Drupal/Component/FileCache/FileCacheFactory.php", "lib/Drupal/Component/Utility/Timer.php", "lib/Drupal/Component/Utility/Unicode.php", "lib/Drupal/Core/Cache/Cache.php", "lib/Drupal/Core/Cache/CacheBackendInterface.php", "lib/Drupal/Core/Cache/CacheTagsChecksumInterface.php", "lib/Drupal/Core/Cache/CacheTagsChecksumTrait.php", "lib/Drupal/Core/Cache/CacheTagsInvalidatorInterface.php", "lib/Drupal/Core/Cache/DatabaseBackend.php", "lib/Drupal/Core/Cache/DatabaseCacheTagsChecksum.php", "lib/Drupal/Core/Database/Connection.php", "lib/Drupal/Core/Database/Database.php", "lib/Drupal/Core/Database/StatementInterface.php", "lib/Drupal/Core/DependencyInjection/Container.php", "lib/Drupal/Core/DrupalKernel.php", "lib/Drupal/Core/DrupalKernelInterface.php", "lib/Drupal/Core/Installer/InstallerRedirectTrait.php", "lib/Drupal/Core/Site/Settings.php", "lib/Drupal/Component/Datetime/Time.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Drupal is an open source content management platform powering millions of websites and applications.", "support": {"source": "https://github.com/drupal/core/tree/11.1.7"}, "time": "2025-05-08T03:56:29+00:00"}, {"name": "drupal/core-composer-scaffold", "version": "11.1.7", "source": {"type": "git", "url": "https://github.com/drupal/core-composer-scaffold.git", "reference": "30e2dce1d08858236ae2703c0a72d120d8075bc5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core-composer-scaffold/zipball/30e2dce1d08858236ae2703c0a72d120d8075bc5", "reference": "30e2dce1d08858236ae2703c0a72d120d8075bc5", "shasum": ""}, "require": {"composer-plugin-api": "^2", "php": ">=7.3.0"}, "conflict": {"drupal-composer/drupal-scaffold": "*"}, "require-dev": {"composer/composer": "^1.8@stable"}, "type": "composer-plugin", "extra": {"class": "Drupal\\Composer\\Plugin\\Scaffold\\Plugin", "branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Drupal\\Composer\\Plugin\\Scaffold\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "A flexible Composer project scaffold builder.", "homepage": "https://www.drupal.org/project/drupal", "keywords": ["drupal"], "support": {"source": "https://github.com/drupal/core-composer-scaffold/tree/11.1.7"}, "time": "2024-11-02T22:49:15+00:00"}, {"name": "drupal/core-project-message", "version": "11.1.7", "source": {"type": "git", "url": "https://github.com/drupal/core-project-message.git", "reference": "d1da83722735cb0f7ccabf9fef7b5607b442c3a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core-project-message/zipball/d1da83722735cb0f7ccabf9fef7b5607b442c3a8", "reference": "d1da83722735cb0f7ccabf9fef7b5607b442c3a8", "shasum": ""}, "require": {"composer-plugin-api": "^2", "php": ">=7.3.0"}, "type": "composer-plugin", "extra": {"class": "Drupal\\Composer\\Plugin\\ProjectMessage\\MessagePlugin"}, "autoload": {"psr-4": {"Drupal\\Composer\\Plugin\\ProjectMessage\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Adds a message after Composer installation.", "homepage": "https://www.drupal.org/project/drupal", "keywords": ["drupal"], "support": {"source": "https://github.com/drupal/core-project-message/tree/11.1.7"}, "time": "2023-07-24T07:55:25+00:00"}, {"name": "drupal/core-recommended", "version": "11.1.7", "source": {"type": "git", "url": "https://github.com/drupal/core-recommended.git", "reference": "bf6c2fcee9e0fd402f32af70632ed8094ced34a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core-recommended/zipball/bf6c2fcee9e0fd402f32af70632ed8094ced34a3", "reference": "bf6c2fcee9e0fd402f32af70632ed8094ced34a3", "shasum": ""}, "require": {"asm89/stack-cors": "~v2.2.0", "composer/semver": "~3.4.3", "doctrine/annotations": "~2.0.2", "doctrine/deprecations": "~1.1.3", "doctrine/lexer": "~2.1.1", "drupal/core": "11.1.7", "egulias/email-validator": "~4.0.2", "guzzlehttp/guzzle": "~7.9.2", "guzzlehttp/promises": "~2.0.4", "guzzlehttp/psr7": "~2.7.0", "masterminds/html5": "~2.9.0", "mck89/peast": "~v1.16.3", "pear/archive_tar": "~1.5.0", "pear/console_getopt": "~v1.4.3", "pear/pear-core-minimal": "~v1.10.16", "pear/pear_exception": "~v1.0.2", "php-tuf/composer-stager": "~v2.0.1", "psr/cache": "~3.0.0", "psr/container": "~2.0.2", "psr/event-dispatcher": "~1.0.0", "psr/http-client": "~1.0.3", "psr/http-factory": "~1.1.0", "psr/log": "~3.0.2", "ralouphie/getallheaders": "~3.0.3", "revolt/event-loop": "~v1.0.6", "sebastian/diff": "~5.1.1", "symfony/console": "~v7.2.0", "symfony/dependency-injection": "~v7.2.0", "symfony/deprecation-contracts": "~v3.5.1", "symfony/error-handler": "~v7.2.0", "symfony/event-dispatcher": "~v7.2.0", "symfony/event-dispatcher-contracts": "~v3.5.1", "symfony/filesystem": "~v7.2.0", "symfony/finder": "~v7.2.0", "symfony/http-foundation": "~v7.2.0", "symfony/http-kernel": "~v7.2.0", "symfony/mailer": "~v7.2.0", "symfony/mime": "~v7.2.0", "symfony/polyfill-ctype": "~v1.31.0", "symfony/polyfill-iconv": "~v1.31.0", "symfony/polyfill-intl-grapheme": "~v1.31.0", "symfony/polyfill-intl-idn": "~v1.31.0", "symfony/polyfill-intl-normalizer": "~v1.31.0", "symfony/polyfill-mbstring": "~v1.31.0", "symfony/process": "~v7.2.0", "symfony/psr-http-message-bridge": "~v7.2.0", "symfony/routing": "~v7.2.0", "symfony/serializer": "~v7.2.0", "symfony/service-contracts": "~v3.5.1", "symfony/string": "~v7.2.0", "symfony/translation-contracts": "~v3.5.1", "symfony/validator": "~v7.2.0", "symfony/var-dumper": "~v7.2.0", "symfony/var-exporter": "~v7.2.0", "symfony/yaml": "~v7.2.0", "twig/twig": "~v3.19.0"}, "conflict": {"webflo/drupal-core-strict": "*"}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Core and its dependencies with known-compatible minor versions. Require this project INSTEAD OF drupal/core.", "support": {"source": "https://github.com/drupal/core-recommended/tree/11.1.7"}, "time": "2025-05-08T03:56:29+00:00"}, {"name": "drupal/crop", "version": "2.4.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/crop.git", "reference": "8.x-2.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/crop-8.x-2.4.zip", "reference": "8.x-2.4", "shasum": "be11fad0abf1d53544d35cb4ca6cedd8e91d2542"}, "require": {"drupal/core": "^9.3 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Drupal Media Team", "homepage": "https://www.drupal.org/user/3260690"}, {"name": "phenaproxima", "homepage": "https://www.drupal.org/user/205645"}, {"name": "slashrsm", "homepage": "https://www.drupal.org/user/744628"}, {"name": "woprrr", "homepage": "https://www.drupal.org/user/858604"}], "description": "Provides storage and API for image crops.", "homepage": "https://www.drupal.org/project/crop", "support": {"source": "https://git.drupalcode.org/project/crop", "issues": "https://www.drupal.org/project/issues/crop"}}, {"name": "drupal/ctools", "version": "4.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/ctools.git", "reference": "4.1.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/ctools-4.1.0.zip", "reference": "4.1.0", "shasum": "69f5889cf557df9e55519390e6a95cfa31b67874"}, "require": {"drupal/core": "^9.5 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "4.1.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "branch-alias": {"dev-8.x-3.x": "3.x-dev"}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON> (EclipseGc)", "homepage": "https://www.drupal.org/u/eclipsegc", "role": "Maintainer"}, {"name": "<PERSON> (japerry)", "homepage": "https://www.drupal.org/u/japerry", "role": "Maintainer"}, {"name": "<PERSON> (tim.plunkett)", "homepage": "https://www.drupal.org/u/timplunkett", "role": "Maintainer"}, {"name": "<PERSON> (neclimdul)", "homepage": "https://www.drupal.org/u/neclimdul", "role": "Maintainer"}, {"name": "<PERSON> (da<PERSON>hn<PERSON>)", "homepage": "https://www.drupal.org/u/dawehner", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/160302"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/26979"}, {"name": "neclimdul", "homepage": "https://www.drupal.org/user/48673"}, {"name": "sdboyer", "homepage": "https://www.drupal.org/user/146719"}, {"name": "sun", "homepage": "https://www.drupal.org/user/54136"}, {"name": "tim.plunkett", "homepage": "https://www.drupal.org/user/241634"}], "description": "Provides a number of utility and helper APIs for Drupal developers and site builders.", "homepage": "https://www.drupal.org/project/ctools", "support": {"source": "https://git.drupalcode.org/project/ctools", "issues": "https://www.drupal.org/project/issues/ctools"}}, {"name": "drupal/custom_breadcrumbs", "version": "1.1.2", "source": {"type": "git", "url": "https://git.drupalcode.org/project/custom_breadcrumbs.git", "reference": "1.1.2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/custom_breadcrumbs-1.1.2.zip", "reference": "1.1.2", "shasum": "89e1019ce67fb3980756b11cb037cf946b3971f0"}, "require": {"drupal/core": "^10 || ^11", "drupal/token": "^1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "1.1.2", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (lamp5)", "homepage": "https://www.drupal.org/u/lamp5", "role": "Maintainer"}, {"name": "Contributors", "homepage": "https://www.drupal.org/node/98576/committers", "role": "Contributors"}, {"name": "MGN", "homepage": "https://www.drupal.org/user/321760"}, {"name": "paul<PERSON>s", "homepage": "https://www.drupal.org/user/3640109"}, {"name": "renatog", "homepage": "https://www.drupal.org/user/3326031"}], "description": "Custom breadcrumbs helps the user to create and manage breadcrumbs menu on all content entity pages and other like views, page manager, controllers.", "homepage": "https://www.drupal.org/project/custom_breadcrumbs", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "http://cgit.drupalcode.org/custom_breadcrumbs", "issues": "https://www.drupal.org/project/issues/custom_breadcrumbs"}}, {"name": "drupal/devel", "version": "5.3.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/devel.git", "reference": "5.3.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/devel-5.3.1.zip", "reference": "5.3.1", "shasum": "6a5f13bdf93dc5f7f194b6af847589ae15e37b63"}, "require": {"doctrine/common": "^2.7 || ^3.4", "drupal/core": "^10.3 || ^11 || ^12", "php": ">=8.1", "symfony/var-dumper": "^4 || ^5 || ^6 || ^7"}, "conflict": {"drupal/core": "<10.3", "drush/drush": "<12.5.1", "kint-php/kint": "<3"}, "require-dev": {"drush/drush": "^13", "firephp/firephp-core": "^0.5.3", "kint-php/kint": "^5.1"}, "suggest": {"kint-php/kint": "Kint provides an informative display of arrays/objects. Useful for debugging and developing."}, "type": "drupal-module", "extra": {"drupal": {"version": "5.3.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "moshe weitzman", "homepage": "https://www.drupal.org/user/23"}], "description": "Various blocks, pages, and functions for developers.", "homepage": "https://www.drupal.org/project/devel", "support": {"source": "https://gitlab.com/drupalspoons/devel", "issues": "https://gitlab.com/drupalspoons/devel/-/issues", "slack": "https://drupal.slack.com/archives/C012WAW1MH6"}}, {"name": "drupal/dropdown_language", "version": "4.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/dropdown_language.git", "reference": "4.1.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/dropdown_language-4.1.0.zip", "reference": "4.1.0", "shasum": "078708dc86e3bde53d39c8018f1eef53909acf58"}, "require": {"drupal/core": "^11", "php": "^8.3"}, "type": "drupal-module", "extra": {"drupal": {"version": "4.1.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/user/291091", "email": "<EMAIL>"}, {"name": "grevil", "homepage": "https://www.drupal.org/user/3668491"}, {"name": "SKAUGHT", "homepage": "https://www.drupal.org/user/297907"}], "description": "Dropdown Language Switcher. https://www.drupal.org/project/dropdown_language", "homepage": "https://www.drupal.org/project/dropdown_language", "support": {"source": "https://git.drupalcode.org/project/dropdown_language"}}, {"name": "drupal/easy_breadcrumb", "version": "2.0.9", "source": {"type": "git", "url": "https://git.drupalcode.org/project/easy_breadcrumb.git", "reference": "2.0.9"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/easy_breadcrumb-2.0.9.zip", "reference": "2.0.9", "shasum": "9e7c33e2ec0637d37d509776795a476f2f2d9bb8"}, "require": {"drupal/core": "^9.2 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.9", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/u/neslee-canil-pinto", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/greg-boggs", "role": "Maintainer"}, {"name": "diamondsea", "homepage": "https://www.drupal.org/user/430714"}, {"name": "greg boggs", "homepage": "https://www.drupal.org/user/153069"}, {"name": "hmartens", "homepage": "https://www.drupal.org/user/622826"}, {"name": "loopduplicate", "homepage": "https://www.drupal.org/user/717290"}, {"name": "neslee canil pinto", "homepage": "https://www.drupal.org/user/3580850"}, {"name": "nick<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3094661"}, {"name": "rakesh.gectcr", "homepage": "https://www.drupal.org/user/1177822"}, {"name": "renatog", "homepage": "https://www.drupal.org/user/3326031"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1667988"}, {"name": "spuky", "homepage": "https://www.drupal.org/user/209353"}, {"name": "tatarbj", "homepage": "https://www.drupal.org/user/649590"}], "description": "Adds configuration to the system breadcrumbs.", "homepage": "https://www.drupal.org/project/easy_breadcrumb", "support": {"source": "https://git.drupalcode.org/project/easy_breadcrumb", "issues": "https://www.drupal.org/project/issues/easy_breadcrumb"}}, {"name": "drupal/entity_reference_revisions", "version": "1.12.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/entity_reference_revisions.git", "reference": "8.x-1.12"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/entity_reference_revisions-8.x-1.12.zip", "reference": "8.x-1.12", "shasum": "2a2ff8617c7ce01b56df1caaf0a563da04948e26"}, "require": {"drupal/core": "^9 || ^10 || ^11"}, "require-dev": {"drupal/diff": "^1 || ^2"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.12", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10 || ^11"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/514222"}, {"name": "jeroen.b", "homepage": "https://www.drupal.org/user/1853532"}, {"name": "mi<PERSON>_dietiker", "homepage": "https://www.drupal.org/user/227761"}], "description": "Entity Reference Revisions", "homepage": "https://www.drupal.org/project/entity_reference_revisions", "support": {"source": "https://git.drupalcode.org/project/entity_reference_revisions"}}, {"name": "drupal/feeds", "version": "3.0.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/feeds.git", "reference": "8.x-3.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/feeds-8.x-3.0.zip", "reference": "8.x-3.0", "shasum": "d6cbfdcc6ece690fe3b20dd62285eea91113089a"}, "require": {"drupal/core": "^10.1 || ^11", "laminas/laminas-feed": "^2.22"}, "require-dev": {"drupal/pathauto": "^1.0", "megachriz/drupalbook": "^1.0 || ^2.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-3.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": ">=9"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "alex_b", "homepage": "https://www.drupal.org/user/53995"}, {"name": "dave reid", "homepage": "https://www.drupal.org/user/53892"}, {"name": "e2thex", "homepage": "https://www.drupal.org/user/189123"}, {"name": "feb<PERSON>ro", "homepage": "https://www.drupal.org/user/43670"}, {"name": "franz", "homepage": "https://www.drupal.org/user/581844"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/user/4736"}, {"name": "jmiccolis", "homepage": "https://www.drupal.org/user/31731"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/160302"}, {"name": "kking", "homepage": "https://www.drupal.org/user/24399"}, {"name": "megachriz", "homepage": "https://www.drupal.org/user/654114"}, {"name": "tobby", "homepage": "https://www.drupal.org/user/154797"}, {"name": "twistor", "homepage": "https://www.drupal.org/user/473738"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/user/32237"}, {"name": "yhahn", "homepage": "https://www.drupal.org/user/264833"}], "description": "Aggregates RSS/Atom/RDF feeds, imports CSV files and more.", "homepage": "https://www.drupal.org/project/feeds", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "https://git.drupalcode.org/project/feeds", "issues": "https://www.drupal.org/project/issues/feeds"}}, {"name": "drupal/image_widget_crop", "version": "3.0.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/image_widget_crop.git", "reference": "3.0.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/image_widget_crop-3.0.0.zip", "reference": "3.0.0", "shasum": "84d83985413f4ecce182d5b52df02ba594ab529b"}, "require": {"drupal/core": "^9.5 || ^10 || ^11", "drupal/crop": "^2"}, "require-dev": {"drupal/crop": "*", "drupal/ctools": "^4.1", "drupal/entity_browser": "^2", "drupal/inline_entity_form": "^1 || ^3"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.0.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/woprrr", "role": "Maintainer"}, {"name": "Drupal media CI", "homepage": "https://www.drupal.org/user/3057985"}, {"name": "Drupal Media Team", "homepage": "https://www.drupal.org/user/3260690"}, {"name": "phenaproxima", "homepage": "https://www.drupal.org/user/205645"}, {"name": "slashrsm", "homepage": "https://www.drupal.org/user/744628"}, {"name": "woprrr", "homepage": "https://www.drupal.org/user/858604"}], "description": "Provides an interface for using the features of the Crop API.", "homepage": "https://www.drupal.org/project/image_widget_crop", "keywords": ["Crop", "<PERSON><PERSON><PERSON>", "Drupal Media"], "support": {"source": "https://www.drupal.org/project/image_widget_crop", "issues": "https://www.drupal.org/project/issues/image_widget_crop", "irc": "irc://irc.freenode.org/drupal-contribute"}}, {"name": "drupal/lang_dropdown", "version": "2.3.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/lang_dropdown.git", "reference": "8.x-2.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/lang_dropdown-8.x-2.3.zip", "reference": "8.x-2.3", "shasum": "939adb961069d4b0f240119190fa6d1fd1771c33"}, "require": {"drupal/core": "^9.3 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "b<PERSON>son_", "homepage": "https://www.drupal.org/user/2393360"}, {"name": "kala4ek", "homepage": "https://www.drupal.org/user/1945174"}, {"name": "manfer", "homepage": "https://www.drupal.org/user/506264"}, {"name": "moham<PERSON> j. r<PERSON>em", "homepage": "https://www.drupal.org/user/255384"}, {"name": "r<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1414312"}], "description": "Provides a dropdown select to switch between available languages.", "homepage": "https://www.drupal.org/project/lang_dropdown", "support": {"source": "https://git.drupalcode.org/project/lang_dropdown"}}, {"name": "drupal/menu_block", "version": "1.14.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/menu_block.git", "reference": "8.x-1.14"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/menu_block-8.x-1.14.zip", "reference": "8.x-1.14", "shasum": "c71e60acaba8a7553e8cf423a56328771cbdedf4"}, "require": {"drupal/core": "^10.1 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.14", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "dave reid", "homepage": "https://www.drupal.org/user/53892"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/160302"}, {"name": "j<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/32095"}, {"name": "kim.pepper", "homepage": "https://www.drupal.org/user/370574"}, {"name": "renatog", "homepage": "https://www.drupal.org/user/3326031"}, {"name": "rrrob", "homepage": "https://www.drupal.org/user/273533"}], "description": "Provides configurable blocks of menu links.", "homepage": "https://www.drupal.org/project/menu_block", "support": {"source": "https://git.drupalcode.org/project/menu_block"}}, {"name": "drupal/menu_item_extras", "version": "3.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/menu_item_extras.git", "reference": "3.1.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/menu_item_extras-3.1.0.zip", "reference": "3.1.0", "shasum": "39abc5557333e71b1f0b26f069e7367f921bdbab"}, "require": {"drupal/core": "^10.3 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.1.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": ">=12"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/u/andriy-khomych", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/u/bogdan-hepting", "role": "Maintainer"}, {"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/u/voleger", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/u/ozin", "role": "Maintainer"}], "description": "Provide an additional custom fields which can be used on Menu link", "homepage": "https://www.drupal.org/project/menu_item_extras", "keywords": ["<PERSON><PERSON><PERSON>", "Mega Menu"], "support": {"source": "https://git.drupalcode.org/project/menu_item_extras", "issues": "https://www.drupal.org/project/issues/menu_item_extras"}}, {"name": "drupal/minifyhtml", "version": "2.0.5", "source": {"type": "git", "url": "https://git.drupalcode.org/project/minifyhtml.git", "reference": "2.0.5"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/minifyhtml-2.0.5.zip", "reference": "2.0.5", "shasum": "e400b98a45257ce0443e17f4e139b94fbea54f93"}, "require": {"drupal/core": "^9 || ^10 || ^11"}, "require-dev": {"drupal/coder": "^8.3", "drupal/core-dev": "^10", "squizlabs/php_codesniffer": "^3.7"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.5", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/user/1846786", "email": "<EMAIL>", "role": "maintainer"}], "description": "Minify the contents of the <PERSON><PERSON><PERSON> page", "homepage": "https://www.drupal.org/project/minifyhtml", "support": {"source": "https://git.drupalcode.org/project/minifyhtml", "issues": "https://www.drupal.org/project/issues/minifyhtml"}}, {"name": "drupal/minifyjs", "version": "3.0.3", "source": {"type": "git", "url": "https://git.drupalcode.org/project/minifyjs.git", "reference": "3.0.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/minifyjs-3.0.3.zip", "reference": "3.0.3", "shasum": "8bfe99660943e16dc05ae79a377a220ea95bf3b2"}, "require": {"drupal/core": "^8.8 || ^9 || ^10 || ^11", "matthiasmullie/minify": "~1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.0.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/user/1846786", "email": "<EMAIL>"}], "description": "Allows an administrator to minify any javascript file in Dr<PERSON>al and use that version rather than the original.", "homepage": "https://www.drupal.org/project/minifyjs", "support": {"source": "https://git.drupalcode.org/project/minifyjs", "issues": "https://www.drupal.org/project/issues/minifyjs"}}, {"name": "drupal/nouislider_js", "version": "15.8.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/nouislider_js.git", "reference": "b5610d5842784581e4c766cd1c3852ca0b355ed5"}, "dist": {"type": "zip", "url": "https://git.drupalcode.org/api/v4/projects/project%2Fnouislider_js/repository/archive.zip?sha=b5610d5842784581e4c766cd1c3852ca0b355ed5", "reference": "b5610d5842784581e4c766cd1c3852ca0b355ed5", "shasum": ""}, "type": "drupal-library", "extra": {"installer-name": "<PERSON>ui<PERSON><PERSON><PERSON>"}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT License"], "description": "Mirror of the noUiSlider javascript library tagged as a Drupal library.", "homepage": "https://github.com/leongersen/noUiSlider", "support": {"source": "https://git.drupalcode.org/project/nouislider_js/-/tree/15.8.0"}, "time": "2025-01-03T10:53:20+00:00"}, {"name": "drupal/paragraphs", "version": "1.19.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/paragraphs.git", "reference": "8.x-1.19"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/paragraphs-8.x-1.19.zip", "reference": "8.x-1.19", "shasum": "831a81a11eac419e8410db45efef5b283c4d117c"}, "require": {"drupal/core": "^10.2 || ^11", "drupal/entity_reference_revisions": "~1.3"}, "require-dev": {"drupal/block_field": "1.x-dev", "drupal/diff": "1.x-dev", "drupal/entity_browser": "2.x-dev", "drupal/entity_usage": "2.x-dev", "drupal/feeds": "^3", "drupal/field_group": "3.x-dev", "drupal/inline_entity_form": "3.x-dev", "drupal/paragraphs-paragraphs_library": "*", "drupal/replicate": "1.x-dev", "drupal/search_api": "^1", "drupal/search_api_db": "*"}, "suggest": {"drupal/entity_browser": "Recommended for an improved user experience when using the Paragraphs library module"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.19", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "frans", "homepage": "https://www.drupal.org/user/514222"}, {"name": "jeroen.b", "homepage": "https://www.drupal.org/user/1853532"}, {"name": "j<PERSON>ller", "homepage": "https://www.drupal.org/user/99012"}, {"name": "mi<PERSON>_dietiker", "homepage": "https://www.drupal.org/user/227761"}, {"name": "primsi", "homepage": "https://www.drupal.org/user/282629"}], "description": "Enables the creation of Paragraphs entities.", "homepage": "https://www.drupal.org/project/paragraphs", "support": {"source": "https://git.drupalcode.org/project/paragraphs"}}, {"name": "drupal/pathauto", "version": "1.13.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/pathauto.git", "reference": "8.x-1.13"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/pathauto-8.x-1.13.zip", "reference": "8.x-1.13", "shasum": "e64b5a82cf1b8ab48bce400b21ae6fc99c8078fd"}, "require": {"drupal/core": "^9.4 || ^10 || ^11", "drupal/ctools": "*", "drupal/token": "*"}, "require-dev": {"drupal/forum": "*"}, "suggest": {"drupal/redirect": "When installed Pathauto will provide a new \"Update Action\" in case your URLs change. This is the recommended update action and is considered the best practice for SEO and usability."}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.13", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "dave reid", "homepage": "https://www.drupal.org/user/53892"}, {"name": "Freso", "homepage": "https://www.drupal.org/user/27504"}, {"name": "greggles", "homepage": "https://www.drupal.org/user/36762"}], "description": "Provides a mechanism for modules to automatically generate aliases for the content they manage.", "homepage": "https://www.drupal.org/project/pathauto", "support": {"source": "https://cgit.drupalcode.org/pathauto", "issues": "https://www.drupal.org/project/issues/pathauto", "documentation": "https://www.drupal.org/docs/8/modules/pathauto"}}, {"name": "drupal/recaptcha", "version": "3.4.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/recaptcha.git", "reference": "8.x-3.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/recaptcha-8.x-3.4.zip", "reference": "8.x-3.4", "shasum": "95fa7ac5dd064ea6a1c14fc4881778bf68200598"}, "require": {"drupal/captcha": "^1.15 || ^2.0", "drupal/core": "^10 || ^11", "google/recaptcha": "^1.3"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-3.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "hass", "homepage": "https://www.drupal.org/u/hass"}, {"name": "See other contributors", "homepage": "https://www.drupal.org/node/147903/committers"}, {"name": "diolan", "homepage": "https://www.drupal.org/user/2336786"}, {"name": "hass", "homepage": "https://www.drupal.org/user/85918"}, {"name": "id.medion", "homepage": "https://www.drupal.org/user/2542592"}, {"name": "kim.pepper", "homepage": "https://www.drupal.org/user/370574"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/395439"}, {"name": "liam morland", "homepage": "https://www.drupal.org/user/493050"}, {"name": "robloach", "homepage": "https://www.drupal.org/user/61114"}, {"name": "wundo", "homepage": "https://www.drupal.org/user/25523"}, {"name": "yseki", "homepage": "https://www.drupal.org/user/1523064"}], "description": "Protect your website from spam and abuse while letting real people pass through with ease.", "homepage": "https://www.drupal.org/project/recaptcha", "support": {"source": "https://git.drupalcode.org/project/recaptcha.git", "issues": "https://www.drupal.org/project/issues/recaptcha"}}, {"name": "drupal/redirect", "version": "1.11.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/redirect.git", "reference": "8.x-1.11"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/redirect-8.x-1.11.zip", "reference": "8.x-1.11", "shasum": "7df8b3524bbde07d254216039636947a689140ef"}, "require": {"drupal/core": "^9.2 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.11", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "dave reid", "homepage": "https://www.drupal.org/user/53892"}, {"name": "kristen pol", "homepage": "https://www.drupal.org/user/8389"}, {"name": "pifagor", "homepage": "https://www.drupal.org/user/2375692"}], "description": "Allows users to redirect from old URLs to new URLs.", "homepage": "https://www.drupal.org/project/redirect", "support": {"source": "https://git.drupalcode.org/project/redirect"}}, {"name": "drupal/rename_admin_paths", "version": "3.0.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/rename_admin_paths.git", "reference": "3.0.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/rename_admin_paths-3.0.0.zip", "reference": "3.0.0", "shasum": "8b91f18d38aec5e651a5cfc1805e4d4559ada617"}, "require": {"drupal/core": ">=10", "php": ">=8.1"}, "require-dev": {"mglaman/phpstan-drupal": "^1.2", "phpstan/extension-installer": "^1.3", "phpstan/phpstan-deprecation-rules": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpstan/phpstan-strict-rules": "^1.5", "phpstan/phpstan-webmozart-assert": "^1.2", "thecodingmachine/phpstan-strict-rules": "^1"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.0.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0"], "authors": [{"name": "ptmkenny", "homepage": "https://www.drupal.org/user/97885"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/410831"}, {"name": "rj<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3457245"}, {"name": "slo<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3516667"}], "description": "This module helps secure the Dr<PERSON><PERSON> backend by overriding the admin path.", "homepage": "https://www.drupal.org/project/rename_admin_paths", "keywords": ["admin", "guess", "override", "protect", "security"], "support": {"source": "https://git.drupalcode.org/project/rename_admin_paths"}}, {"name": "drupal/scheduler", "version": "2.2.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/scheduler.git", "reference": "2.2.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/scheduler-2.2.1.zip", "reference": "2.2.1", "shasum": "ce9a9405ea88140fbdd53f1e3ed3741b6f27266c"}, "require": {"drupal/core": "^8 || ^9 || ^10 || ^11"}, "require-dev": {"drupal/commerce": "^2 || ^3", "drupal/devel_generate": ">=4", "drupal/rules": "^3 || ^4", "drupal/workbench_moderation": "*", "drupal/workbench_moderation_actions": "*", "drush/drush": ">=9"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.2.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON> (<PERSON>)", "homepage": "https://www.drupal.org/u/eric-schaefer", "role": "Maintainer"}, {"name": "<PERSON> (jonathan1055)", "homepage": "https://www.drupal.org/u/jonathan1055", "role": "Maintainer"}, {"name": "<PERSON> (pfrenssen)", "homepage": "https://www.drupal.org/u/pfrenssen", "role": "Maintainer"}, {"name": "<PERSON> (rickmanelius)", "homepage": "https://www.drupal.org/u/rickmanelius", "role": "Maintainer"}], "description": "Automatically publish and unpublish content at specified dates and times.", "homepage": "https://drupal.org/project/scheduler", "support": {"source": "https://git.drupalcode.org/project/scheduler", "issues": "https://www.drupal.org/project/issues/scheduler"}}, {"name": "drupal/search_api", "version": "1.38.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/search_api.git", "reference": "8.x-1.38"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/search_api-8.x-1.38.zip", "reference": "8.x-1.38", "shasum": "d1c83ba74e553eca07d3ea4b15e5d9c7f009a496"}, "require": {"drupal/core": "^10.2 || ^11"}, "conflict": {"drupal/search_api_solr": "2.* || 3.0 || 3.1"}, "require-dev": {"drupal/language_fallback_fix": "@dev", "drupal/search_api_autocomplete": "@dev", "drupal/search_api_db": "*"}, "suggest": {"drupal/facets": "Adds the ability to create faceted searches.", "drupal/search_api_autocomplete": "Allows adding autocomplete suggestions to search fields.", "drupal/search_api_solr": "Adds support for using Apache Solr as a backend."}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.38", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/drunken-monkey"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/nick_vh"}, {"name": "See other contributors", "homepage": "https://www.drupal.org/node/790418/committers"}], "description": "Provides a generic framework for modules offering search capabilities.", "homepage": "https://www.drupal.org/project/search_api", "support": {"source": "https://git.drupalcode.org/project/search_api", "issues": "https://www.drupal.org/project/issues/search_api", "irc": "irc://irc.freenode.org/drupal-search-api"}}, {"name": "drupal/search_api_solr", "version": "4.3.10", "source": {"type": "git", "url": "https://git.drupalcode.org/project/search_api_solr.git", "reference": "4.3.10"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/search_api_solr-4.3.10.zip", "reference": "4.3.10", "shasum": "8d37467f0f2ab34ed6a0b61872ce7ff8e5e64730"}, "require": {"composer-runtime-api": ">=2.0", "composer/semver": "^1.0|^3.0", "consolidation/annotated-command": "^2.12|^4.1", "drupal/core": "^10.2 || ^11.0", "drupal/search_api": "^1.37|1.x-dev", "ext-dom": "*", "ext-json": "*", "ext-simplexml": "*", "laminas/laminas-stdlib": "^3.2", "maennchen/zipstream-php": "^2.2.1|^3.0.2", "solarium/solarium": "^6.3.7"}, "conflict": {"drupal/acquia_search_solr": "<1.0.0-beta8", "drupal/search_api_autocomplete": "<1.6.0", "drupal/search_api_solr_multilingual": "<3.0.0"}, "require-dev": {"drupal/devel": "^4.0|^5.0", "drupal/facets": "^3.0.x-dev", "drupal/facets_exposed_filters": "*", "drupal/geofield": "1.x-dev", "drupal/search_api_autocomplete": "1.x-dev", "drupal/search_api_location": "1.x-dev", "drupal/search_api_spellcheck": "3.x-dev", "drupal/views-views": "*", "monolog/monolog": "^1.25|^3"}, "suggest": {"drupal/facets": "Provides facetted search.", "drupal/search_api_autocomplete": "Provides auto complete for search boxes.", "drupal/search_api_location": "Provides location searches.", "drupal/search_api_solr_nlp": "Highly recommended! Provides Solr field types based on natural language processing (NLP).", "drupal/search_api_spellcheck": "Provides spell checking and 'Did You Mean?'."}, "type": "drupal-module", "extra": {"drupal": {"version": "4.3.10", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": ">=9"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/mkalkbrenner"}, {"name": "Other contributors", "homepage": "https://www.drupal.org/node/982682/committers"}, {"name": "c<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/419305"}, {"name": "drunken monkey", "homepage": "https://www.drupal.org/user/205582"}, {"name": "mkalkbrenner", "homepage": "https://www.drupal.org/user/124705"}, {"name": "nick_vh", "homepage": "https://www.drupal.org/user/122682"}], "description": "Offers an implementation of the Search API that uses an Apache Solr server for indexing content.", "homepage": "https://www.drupal.org/project/search_api_solr", "support": {"source": "http://git.drupal.org/project/search_api_solr.git", "issues": "https://www.drupal.org/project/issues/search_api_solr", "chat": "https://drupalchat.me/channel/search"}}, {"name": "drupal/social_media", "version": "2.0.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/social_media.git", "reference": "2.0.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/social_media-2.0.1.zip", "reference": "2.0.1", "shasum": "c0d6ac7e7c0da066dc0cc0b022ecc44ffbb70604"}, "require": {"drupal/core": "^10.3 || ^11", "drupal/token": "^1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "http://drupalsharing.com/", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/user/953390", "role": "Maintainer"}, {"name": "takim", "homepage": "https://www.drupal.org/user/252386"}], "description": "Share current page to social media", "homepage": "https://drupal.org/project/social_media", "support": {"source": "http://cgit.drupalcode.org/social_media", "issues": "https://www.drupal.org/project/issues/social_media"}}, {"name": "drupal/social_media_links", "version": "2.10.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/social_media_links.git", "reference": "8.x-2.10"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/social_media_links-8.x-2.10.zip", "reference": "8.x-2.10", "shasum": "dd329d44f88112d2fe83f54331502738c5f2810b"}, "require": {"drupal/core": "^9.5 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.10", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/u/neslee-canil-pinto", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/cbeier", "role": "Maintainer"}, {"name": "neslee canil pinto", "homepage": "https://www.drupal.org/user/3580850"}], "description": "The module provides a block that display links (icons) to your profiles on various social networking sites.", "homepage": "https://www.drupal.org/project/social_media_links", "support": {"source": "https://git.drupalcode.org/project/social_media_links", "issues": "https://www.drupal.org/project/issues/social_media_links"}}, {"name": "drupal/svg_image", "version": "3.2.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/svg_image.git", "reference": "3.2.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/svg_image-3.2.1.zip", "reference": "3.2.1", "shasum": "4623b9d0de4c624857df10daaa8c68793942ad87"}, "require": {"drupal/core": "^10.3 || ^11", "enshrined/svg-sanitize": ">=0.15 <1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.2.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/u/imyaro", "role": "Maintainer"}, {"name": "See contributors", "homepage": "https://www.drupal.org/node/2887125/committers"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/2870933"}, {"name": "mably", "homepage": "https://www.drupal.org/user/3375160"}, {"name": "thomas.fro<PERSON>ter", "homepage": "https://www.drupal.org/user/409335"}], "description": "Overrides the standard image formatter and widget to support SVG files.", "homepage": "https://drupal.org/project/svg_image", "support": {"source": "https://git.drupalcode.org/project/svg_image", "issues": "https://www.drupal.org/project/issues/svg_image"}}, {"name": "drupal/token", "version": "1.15.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/token.git", "reference": "8.x-1.15"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/token-8.x-1.15.zip", "reference": "8.x-1.15", "shasum": "5916fbccc86458a5f51e71f832ac70ff4c84ebdf"}, "require": {"drupal/core": "^9.2 || ^10 || ^11"}, "require-dev": {"drupal/book": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.15", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": ">=9"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "be<PERSON>r", "homepage": "https://www.drupal.org/user/214652"}, {"name": "dave reid", "homepage": "https://www.drupal.org/user/53892"}, {"name": "eaton", "homepage": "https://www.drupal.org/user/16496"}, {"name": "fago", "homepage": "https://www.drupal.org/user/16747"}, {"name": "greggles", "homepage": "https://www.drupal.org/user/36762"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/4420"}], "description": "Provides a user interface for the Token API, some missing core tokens.", "homepage": "https://www.drupal.org/project/token", "support": {"source": "https://git.drupalcode.org/project/token"}}, {"name": "drupal/translatable_config_pages", "version": "1.0.5", "source": {"type": "git", "url": "https://git.drupalcode.org/project/translatable_config_pages.git", "reference": "1.0.5"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/translatable_config_pages-1.0.5.zip", "reference": "1.0.5", "shasum": "f97c1128941684ee6cfeebf7686e85e4e160fc56"}, "require": {"drupal/core": "^8.5 | ^9 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "1.0.5", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "lui<PERSON>a", "homepage": "https://www.drupal.org/user/1022312"}], "description": "Adds an entity to create translatable config pages.", "homepage": "https://www.drupal.org/project/translatable_config_pages", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "https://git.drupalcode.org/project/translatable_config_pages", "issues": "https://www.drupal.org/project/issues/translatable_config_pages"}}, {"name": "drupal/twig_tweak", "version": "3.4.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/twig_tweak.git", "reference": "3.4.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/twig_tweak-3.4.0.zip", "reference": "3.4.0", "shasum": "1f47f71b4cfbad97fff11db1adc72c311bb1645e"}, "require": {"drupal/core": "^10.3 || ^11.0", "ext-json": "*", "php": ">=8.1", "twig/twig": "^3.10.3"}, "suggest": {"symfony/var-dumper": "Better dump() function for debugging Twig variables"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.4.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^10 || ^11"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "chi", "homepage": "https://www.drupal.org/user/556138"}], "description": "A Twig extension with some useful functions and filters for Drup<PERSON> development.", "homepage": "https://www.drupal.org/project/twig_tweak", "keywords": ["<PERSON><PERSON><PERSON>", "Twig"], "support": {"source": "https://git.drupalcode.org/project/twig_tweak", "issues": "https://www.drupal.org/project/issues/twig_tweak"}}, {"name": "drupal/upgrade_status", "version": "4.3.7", "source": {"type": "git", "url": "https://git.drupalcode.org/project/upgrade_status.git", "reference": "4.3.7"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/upgrade_status-4.3.7.zip", "reference": "4.3.7", "shasum": "062d43be89caf5f4b2a463fe9bd35ab155ad714d"}, "require": {"dekor/php-array-table": "^2.0", "drupal/core": "^9 || ^10 || ^11", "mglaman/phpstan-drupal": "^1.2.11", "nikic/php-parser": "^4.0.0|^5.0.0", "phpstan/phpstan-deprecation-rules": "^1.0.0", "symfony/process": "^3.4|^4.0|^5.0|^6.0|^7.0", "webflo/drupal-finder": "^1.2"}, "require-dev": {"drush/drush": "^11|^12|^13"}, "type": "drupal-module", "extra": {"drupal": {"version": "4.3.7", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/4166"}], "description": "Review Drupal major upgrade readiness of the environment and components of the site.", "homepage": "http://drupal.org/project/upgrade_status", "support": {"source": "https://git.drupalcode.org/project/upgrade_status"}}, {"name": "drupal/views_dependent_filters", "version": "1.3.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/views_dependent_filters.git", "reference": "8.x-1.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/views_dependent_filters-8.x-1.3.zip", "reference": "8.x-1.3", "shasum": "f79d8a3cd1e5cfa6e7bcad4999dc06ab9f79a7ba"}, "require": {"drupal/core": "^8.8 || ^9 || ^10 || ^11"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/368854"}, {"name": "jibus", "homepage": "https://www.drupal.org/user/1679812"}, {"name": "joa<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/107701"}, {"name": "nit3ch", "homepage": "https://www.drupal.org/user/772996"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/462700"}], "description": "A Drupal module that provides dependent filters for views.", "homepage": "https://www.drupal.org/project/views_dependent_filters", "support": {"source": "https://git.drupalcode.org/project/views_dependent_filters"}}, {"name": "drupal/webform", "version": "6.3.0-beta2", "source": {"type": "git", "url": "https://git.drupalcode.org/project/webform.git", "reference": "6.3.0-beta2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/webform-6.3.0-beta2.zip", "reference": "6.3.0-beta2", "shasum": "02e45bf3716c6e81846a9b77837782a43053e6f9"}, "require": {"drupal/core": "^10.3 || ^11.0"}, "require-dev": {"drupal/address": "^2.0", "drupal/captcha": "^2.0", "drupal/chosen": "^4.0", "drupal/clientside_validation": "^4.1", "drupal/clientside_validation_jquery": "*", "drupal/devel": "^5.3", "drupal/entity": "^1.5", "drupal/entity_print": "^2.15", "drupal/hal": "^2.0", "drupal/jquery_ui": "^1.7", "drupal/jquery_ui_button": "^2.1", "drupal/jquery_ui_checkboxradio": "^2.1", "drupal/jquery_ui_datepicker": "^2.1", "drupal/mailsystem": "^4.5", "drupal/metatag": "^2.0", "drupal/paragraphs": "^1.18", "drupal/select2": "1.x-dev", "drupal/smtp": "^1.4", "drupal/styleguide": "^2.1", "drupal/telephone_validation": "2.x-dev", "drupal/token": "^1.15", "drupal/webform_access": "*", "drupal/webform_attachment": "*", "drupal/webform_clientside_validation": "*", "drupal/webform_devel": "*", "drupal/webform_entity_print": "*", "drupal/webform_node": "*", "drupal/webform_options_limit": "*", "drupal/webform_scheduled_email": "*", "drupal/webform_share": "*", "drupal/webform_ui": "*"}, "suggest": {"drupal/jquery_ui_buttons": "Provides jQuery UI Checkboxradio library. Required by the Webform jQueryUI Buttons module. The Webform jQueryUI Buttons module is deprecated because jQueryUI is no longer maintained.", "drupal/jquery_ui_datepicker": "Provides jQuery UI Datepicker library. Required to support datepickers. The Webform jQueryUI Datepicker module is deprecated because jQueryUI is no longer maintained."}, "type": "drupal-module", "extra": {"drupal": {"version": "6.3.0-beta2", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Beta releases are not covered by Drupal security advisories."}}, "drush": {"services": {"drush.services.yml": ">=11"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON> (jrockowitz)", "homepage": "https://www.drupal.org/u/jrockowitz", "role": "Maintainer"}, {"name": "Contributors", "homepage": "https://www.drupal.org/node/7404/committers", "role": "Contributor"}, {"name": "liam morland", "homepage": "https://www.drupal.org/user/493050"}, {"name": "mandclu", "homepage": "https://www.drupal.org/user/52136"}, {"name": "quicksketch", "homepage": "https://www.drupal.org/user/35821"}], "description": "Enables the creation of webforms and questionnaires.", "homepage": "https://drupal.org/project/webform", "support": {"source": "https://git.drupalcode.org/project/webform", "issues": "https://www.drupal.org/project/issues/webform?version=8.x", "docs": "https://www.drupal.org/docs/8/modules/webform", "forum": "https://drupal.stackexchange.com/questions/tagged/webform"}}, {"name": "drupal/webp", "version": "1.0.0-rc2", "source": {"type": "git", "url": "https://git.drupalcode.org/project/webp.git", "reference": "8.x-1.0-rc2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/webp-8.x-1.0-rc2.zip", "reference": "8.x-1.0-rc2", "shasum": "16a1d2c29af57ac04603bcba275e732340491554"}, "require": {"drupal/core": "^8 || ^9 || ^10 || ^11", "ext-gd": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.0-rc2", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "RC releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/user/145609", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/user/1133754"}, {"name": "mandclu", "homepage": "https://www.drupal.org/user/52136"}], "description": "Generates WebP copies of image style derivatives.", "homepage": "https://www.drupal.org/project/webp", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "http://cgit.drupalcode.org/webp", "issues": "https://www.drupal.org/project/issues/webp"}}, {"name": "drush/drush", "version": "13.6.0", "source": {"type": "git", "url": "https://github.com/drush-ops/drush.git", "reference": "570a05dce7aea9770f17306808804290764127ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drush-ops/drush/zipball/570a05dce7aea9770f17306808804290764127ad", "reference": "570a05dce7aea9770f17306808804290764127ad", "shasum": ""}, "require": {"chi-teck/drupal-code-generator": "^3.6 || ^4@alpha", "composer-runtime-api": "^2.2", "composer/semver": "^1.4 || ^3", "consolidation/annotated-command": "^4.9.2", "consolidation/config": "^2.1.2 || ^3", "consolidation/filter-via-dot-access-data": "^2.0.2", "consolidation/output-formatters": "^4.3.2", "consolidation/robo": "^4.0.6 || ^5", "consolidation/site-alias": "^4", "consolidation/site-process": "^5.2.0", "dflydev/dot-access-data": "^3.0.2", "ext-dom": "*", "grasmash/yaml-cli": "^3.1", "guzzlehttp/guzzle": "^7.0", "laravel/prompts": "^0.3.5", "league/container": "^4.2", "php": ">=8.2", "psy/psysh": "~0.12", "symfony/event-dispatcher": "^6 || ^7", "symfony/filesystem": "^6.1 || ^7", "symfony/finder": "^6 || ^7", "symfony/var-dumper": "^6.0 || ^7", "symfony/yaml": "^6.0 || ^7"}, "conflict": {"drupal/core": "< 10.2", "drupal/migrate_run": "*", "drupal/migrate_tools": "<= 5"}, "require-dev": {"composer/installers": "^2", "cweagans/composer-patches": "~1.7.3", "drupal/core-recommended": "^10.2.5 || 11.x-dev", "drupal/semver_example": "2.3.0", "jetbrains/phpstorm-attributes": "^1.0", "mglaman/phpstan-drupal": "^1.2", "phpunit/phpunit": "^9 || ^10", "rector/rector": "^1", "squizlabs/php_codesniffer": "^3.7"}, "bin": ["drush", "drush.php"], "type": "library", "extra": {"installer-paths": {"sut/core": ["type:drupal-core"], "sut/libraries/{$name}": ["type:drupal-library"], "sut/themes/unish/{$name}": ["drupal/empty_theme"], "sut/drush/contrib/{$name}": ["type:drupal-drush"], "sut/modules/unish/{$name}": ["drupal/devel"], "sut/themes/contrib/{$name}": ["type:drupal-theme"], "sut/modules/contrib/{$name}": ["type:drupal-module"], "sut/profiles/contrib/{$name}": ["type:drupal-profile"]}}, "autoload": {"psr-4": {"Drush\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "j<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Drush is a command line shell and scripting interface for <PERSON><PERSON><PERSON>, a veritable Swiss Army knife designed to make life easier for those of us who spend some of our working hours hacking away at the command prompt.", "homepage": "http://www.drush.org", "support": {"forum": "http://drupal.stackexchange.com/questions/tagged/drush", "issues": "https://github.com/drush-ops/drush/issues", "security": "https://github.com/drush-ops/drush/security/advisories", "slack": "https://drupal.slack.com/messages/C62H9CWQM", "source": "https://github.com/drush-ops/drush/tree/13.6.0"}, "funding": [{"url": "https://github.com/weitzman", "type": "github"}], "time": "2025-04-22T12:14:13+00:00"}, {"name": "egulias/email-validator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "shasum": ""}, "require": {"doctrine/lexer": "^2.0 || ^3.0", "php": ">=8.1", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.4"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2025-03-06T22:45:56+00:00"}, {"name": "enshrined/svg-sanitize", "version": "0.21.0", "source": {"type": "git", "url": "https://github.com/darylldoyle/svg-sanitizer.git", "reference": "5e477468fac5c5ce933dce53af3e8e4e58dcccc9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/darylldoyle/svg-sanitizer/zipball/5e477468fac5c5ce933dce53af3e8e4e58dcccc9", "reference": "5e477468fac5c5ce933dce53af3e8e4e58dcccc9", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^6.5 || ^8.5"}, "type": "library", "autoload": {"psr-4": {"enshrined\\svgSanitize\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An SVG sanitizer for PHP", "support": {"issues": "https://github.com/darylldoyle/svg-sanitizer/issues", "source": "https://github.com/darylldoyle/svg-sanitizer/tree/0.21.0"}, "time": "2025-01-13T09:32:25+00:00"}, {"name": "google/recaptcha", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/google/recaptcha.git", "reference": "d59a801e98a4e9174814a6d71bbc268dff1202df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/google/recaptcha/zipball/d59a801e98a4e9174814a6d71bbc268dff1202df", "reference": "d59a801e98a4e9174814a6d71bbc268dff1202df", "shasum": ""}, "require": {"php": ">=8"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.14", "php-coveralls/php-coveralls": "^2.5", "phpunit/phpunit": "^10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"ReCaptcha\\": "src/ReCaptcha"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Client library for reCAPTCHA, a free service that protects websites from spam and abuse.", "homepage": "https://www.google.com/recaptcha/", "keywords": ["Abuse", "<PERSON><PERSON>a", "recaptcha", "spam"], "support": {"forum": "https://groups.google.com/forum/#!forum/recaptcha", "issues": "https://github.com/google/recaptcha/issues", "source": "https://github.com/google/recaptcha"}, "time": "2023-02-18T17:41:46+00:00"}, {"name": "grasmash/expander", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/grasmash/expander.git", "reference": "eea11b9afb0c32483b18b9009f4ca07b770e39f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grasmash/expander/zipball/eea11b9afb0c32483b18b9009f4ca07b770e39f4", "reference": "eea11b9afb0c32483b18b9009f4ca07b770e39f4", "shasum": ""}, "require": {"dflydev/dot-access-data": "^3.0.0", "php": ">=8.0", "psr/log": "^2 | ^3"}, "require-dev": {"greg-1-anderson/composer-test-scenarios": "^1", "php-coveralls/php-coveralls": "^2.5", "phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Grasmash\\Expander\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Expands internal property references in PHP arrays file.", "support": {"issues": "https://github.com/grasmash/expander/issues", "source": "https://github.com/grasmash/expander/tree/3.0.1"}, "time": "2024-11-25T23:28:05+00:00"}, {"name": "grasmash/yaml-cli", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/grasmash/yaml-cli.git", "reference": "09a8860566958a1576cc54bbe910a03477e54971"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grasmash/yaml-cli/zipball/09a8860566958a1576cc54bbe910a03477e54971", "reference": "09a8860566958a1576cc54bbe910a03477e54971", "shasum": ""}, "require": {"dflydev/dot-access-data": "^3", "php": ">=8.0", "symfony/console": "^6 || ^7", "symfony/filesystem": "^6 || ^7", "symfony/yaml": "^6 || ^7"}, "require-dev": {"php-coveralls/php-coveralls": "^2", "phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.0"}, "bin": ["bin/yaml-cli"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Grasmash\\YamlCli\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A command line tool for reading and manipulating yaml files.", "support": {"issues": "https://github.com/grasmash/yaml-cli/issues", "source": "https://github.com/grasmash/yaml-cli/tree/3.2.1"}, "time": "2024-04-23T02:10:57+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2025-03-27T13:37:11+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/f9c436286ab2892c7db7be8c8da4ef61ccf7b455", "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.4"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2024-10-17T10:06:22+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-03-27T12:30:47+00:00"}, {"name": "halaxa/json-machine", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/halaxa/json-machine.git", "reference": "5a5cde093181198bd68d94a3d35f80e76720766b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/halaxa/json-machine/zipball/5a5cde093181198bd68d94a3d35f80e76720766b", "reference": "5a5cde093181198bd68d94a3d35f80e76720766b", "shasum": ""}, "require": {"php": "7.2 - 8.4"}, "require-dev": {"ext-json": "*", "friendsofphp/php-cs-fixer": "^3.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.0"}, "suggest": {"ext-json": "To run JSON Machine out of the box without custom decoders.", "guzzlehttp/guzzle": "To run example with GuzzleHttp"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"JsonMachine\\": "src/"}, "exclude-from-classmap": ["src/autoloader.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Efficient, easy-to-use and fast JSON pull parser", "support": {"issues": "https://github.com/halaxa/json-machine/issues", "source": "https://github.com/halaxa/json-machine/tree/1.2.2"}, "funding": [{"url": "https://ko-fi.com/G2G57KTE4", "type": "other"}], "time": "2025-04-30T19:58:47+00:00"}, {"name": "laminas/laminas-escaper", "version": "2.17.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-escaper.git", "reference": "df1ef9503299a8e3920079a16263b578eaf7c3ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-escaper/zipball/df1ef9503299a8e3920079a16263b578eaf7c3ba", "reference": "df1ef9503299a8e3920079a16263b578eaf7c3ba", "shasum": ""}, "require": {"ext-ctype": "*", "ext-mbstring": "*", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-escaper": "*"}, "require-dev": {"infection/infection": "^0.29.8", "laminas/laminas-coding-standard": "~3.0.1", "phpunit/phpunit": "^10.5.45", "psalm/plugin-phpunit": "^0.19.2", "vimeo/psalm": "^6.6.2"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Escaper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Securely and safely escape HTML, HTML attributes, JavaScript, CSS, and URLs", "homepage": "https://laminas.dev", "keywords": ["escaper", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-escaper/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-escaper/issues", "rss": "https://github.com/laminas/laminas-escaper/releases.atom", "source": "https://github.com/laminas/laminas-escaper"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-05-06T19:29:36+00:00"}, {"name": "laminas/laminas-feed", "version": "2.23.1", "source": {"type": "git", "url": "https://github.com/laminas/laminas-feed.git", "reference": "7c6755695cdca18f983d8a116bfa6a55439b1a94"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-feed/zipball/7c6755695cdca18f983d8a116bfa6a55439b1a94", "reference": "7c6755695cdca18f983d8a116bfa6a55439b1a94", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "laminas/laminas-escaper": "^2.9", "laminas/laminas-stdlib": "^3.6", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"laminas/laminas-servicemanager": "<3.3", "zendframework/zend-feed": "*"}, "require-dev": {"laminas/laminas-cache": "^2.13.2 || ^3.12", "laminas/laminas-cache-storage-adapter-memory": "^1.1.0 || ^2.3", "laminas/laminas-coding-standard": "~2.5.0", "laminas/laminas-db": "^2.18", "laminas/laminas-http": "^2.19", "laminas/laminas-servicemanager": "^3.22.1", "laminas/laminas-validator": "^2.46", "phpunit/phpunit": "^10.5.5", "psalm/plugin-phpunit": "^0.19.0", "psr/http-message": "^2.0", "vimeo/psalm": "^5.18.0"}, "suggest": {"laminas/laminas-cache": "Laminas\\Cache component, for optionally caching feeds between requests", "laminas/laminas-db": "Laminas\\Db component, for use with PubSubHubbub", "laminas/laminas-http": "Laminas\\Http for PubSubHubbub, and optionally for use with Laminas\\Feed\\Reader", "laminas/laminas-servicemanager": "Laminas\\ServiceManager component, for easily extending ExtensionManager implementations", "laminas/laminas-validator": "Laminas\\Validator component, for validating email addresses used in Atom feeds and entries when using the Writer subcomponent", "psr/http-message": "PSR-7 ^1.0.1, if you wish to use Laminas\\Feed\\Reader\\Http\\Psr7ResponseDecorator"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Feed\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides functionality for creating and consuming RSS and Atom feeds", "homepage": "https://laminas.dev", "keywords": ["atom", "feed", "laminas", "rss"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-feed/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-feed/issues", "rss": "https://github.com/laminas/laminas-feed/releases.atom", "source": "https://github.com/laminas/laminas-feed"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-05-13T09:35:51+00:00"}, {"name": "laminas/laminas-stdlib", "version": "3.20.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-stdlib.git", "reference": "8974a1213be42c3e2f70b2c27b17f910291ab2f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-stdlib/zipball/8974a1213be42c3e2f70b2c27b17f910291ab2f4", "reference": "8974a1213be42c3e2f70b2c27b17f910291ab2f4", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-stdlib": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^3.0", "phpbench/phpbench": "^1.3.1", "phpunit/phpunit": "^10.5.38", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Stdlib\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "SPL extensions, array utilities, error handlers, and more", "homepage": "https://laminas.dev", "keywords": ["laminas", "stdlib"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-stdlib/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-stdlib/issues", "rss": "https://github.com/laminas/laminas-stdlib/releases.atom", "source": "https://github.com/laminas/laminas-stdlib"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-10-29T13:46:07+00:00"}, {"name": "laravel/prompts", "version": "v0.3.5", "source": {"type": "git", "url": "https://github.com/laravel/prompts.git", "reference": "57b8f7efe40333cdb925700891c7d7465325d3b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/prompts/zipball/57b8f7efe40333cdb925700891c7d7465325d3b1", "reference": "57b8f7efe40333cdb925700891c7d7465325d3b1", "shasum": ""}, "require": {"composer-runtime-api": "^2.2", "ext-mbstring": "*", "php": "^8.1", "symfony/console": "^6.2|^7.0"}, "conflict": {"illuminate/console": ">=10.17.0 <10.25.0", "laravel/framework": ">=10.17.0 <10.25.0"}, "require-dev": {"illuminate/collections": "^10.0|^11.0|^12.0", "mockery/mockery": "^1.5", "pestphp/pest": "^2.3|^3.4", "phpstan/phpstan": "^1.11", "phpstan/phpstan-mockery": "^1.1"}, "suggest": {"ext-pcntl": "Required for the spinner to be animated."}, "type": "library", "extra": {"branch-alias": {"dev-main": "0.3.x-dev"}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Laravel\\Prompts\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Add beautiful and user-friendly forms to your command-line applications.", "support": {"issues": "https://github.com/laravel/prompts/issues", "source": "https://github.com/laravel/prompts/tree/v0.3.5"}, "time": "2025-02-11T13:34:40+00:00"}, {"name": "league/container", "version": "4.2.4", "source": {"type": "git", "url": "https://github.com/thephpleague/container.git", "reference": "7ea728b013b9a156c409c6f0fc3624071b742dec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/container/zipball/7ea728b013b9a156c409c6f0fc3624071b742dec", "reference": "7ea728b013b9a156c409c6f0fc3624071b742dec", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "psr/container": "^1.1 || ^2.0"}, "provide": {"psr/container-implementation": "^1.0"}, "replace": {"orno/di": "~2.0"}, "require-dev": {"nette/php-generator": "^3.4", "nikic/php-parser": "^4.10", "phpstan/phpstan": "^0.12.47", "phpunit/phpunit": "^8.5.17", "roave/security-advisories": "dev-latest", "scrutinizer/ocular": "^1.8", "squizlabs/php_codesniffer": "^3.6"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev", "dev-2.x": "2.x-dev", "dev-3.x": "3.x-dev", "dev-4.x": "4.x-dev", "dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"League\\Container\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A fast and intuitive dependency injection container.", "homepage": "https://github.com/thephpleague/container", "keywords": ["container", "dependency", "di", "injection", "league", "provider", "service"], "support": {"issues": "https://github.com/thephpleague/container/issues", "source": "https://github.com/thephpleague/container/tree/4.2.4"}, "funding": [{"url": "https://github.com/philipobenito", "type": "github"}], "time": "2024-11-10T12:42:13+00:00"}, {"name": "maennchen/zipstream-php", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "aeadcf5c412332eb426c0f9b4485f6accba2a99f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/aeadcf5c412332eb426c0f9b4485f6accba2a99f", "reference": "aeadcf5c412332eb426c0f9b4485f6accba2a99f", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-zlib": "*", "php-64bit": "^8.2"}, "require-dev": {"brianium/paratest": "^7.7", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.16", "guzzlehttp/guzzle": "^7.5", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.5", "phpunit/phpunit": "^11.0", "vimeo/psalm": "^6.0"}, "suggest": {"guzzlehttp/psr7": "^2.4", "psr/http-message": "^2.0"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/3.1.2"}, "funding": [{"url": "https://github.com/maennchen", "type": "github"}], "time": "2025-01-27T12:07:53+00:00"}, {"name": "markbaker/complex", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.2"}, "time": "2022-12-06T16:21:08+00:00"}, {"name": "markbaker/matrix", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "728434227fe21be27ff6d86621a1b13107a2562c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/728434227fe21be27ff6d86621a1b13107a2562c", "reference": "728434227fe21be27ff6d86621a1b13107a2562c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.1"}, "time": "2022-12-02T22:17:43+00:00"}, {"name": "masterminds/html5", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7 || ^8 || ^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.9.0"}, "time": "2024-03-31T07:05:07+00:00"}, {"name": "matthi<PERSON><PERSON><PERSON>/minify", "version": "1.3.73", "source": {"type": "git", "url": "https://github.com/matthiasmullie/minify.git", "reference": "cb7a9297b4ab070909cefade30ee95054d4ae87a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/matthiasmullie/minify/zipball/cb7a9297b4ab070909cefade30ee95054d4ae87a", "reference": "cb7a9297b4ab070909cefade30ee95054d4ae87a", "shasum": ""}, "require": {"ext-pcre": "*", "matthiasmullie/path-converter": "~1.1", "php": ">=5.3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": ">=2.0", "matthiasmullie/scrapbook": ">=1.3", "phpunit/phpunit": ">=4.8", "squizlabs/php_codesniffer": ">=3.0"}, "suggest": {"psr/cache-implementation": "Cache implementation to use with Minify::cache"}, "bin": ["bin/minifycss", "bin/minifyjs"], "type": "library", "autoload": {"psr-4": {"MatthiasMullie\\Minify\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.mullie.eu", "role": "Developer"}], "description": "CSS & JavaScript minifier, in PHP. Removes whitespace, strips comments, combines files (incl. @import statements and small assets in CSS files), and optimizes/shortens a few common programming patterns.", "homepage": "https://github.com/matthiasmullie/minify", "keywords": ["JS", "css", "javascript", "minifier", "minify"], "support": {"issues": "https://github.com/matthiasmullie/minify/issues", "source": "https://github.com/matthiasmullie/minify/tree/1.3.73"}, "funding": [{"url": "https://github.com/matthiasmullie", "type": "github"}], "time": "2024-03-15T10:27:10+00:00"}, {"name": "mat<PERSON><PERSON><PERSON><PERSON>/path-converter", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/matthiasmullie/path-converter.git", "reference": "e7d13b2c7e2f2268e1424aaed02085518afa02d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/matthiasmullie/path-converter/zipball/e7d13b2c7e2f2268e1424aaed02085518afa02d9", "reference": "e7d13b2c7e2f2268e1424aaed02085518afa02d9", "shasum": ""}, "require": {"ext-pcre": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "type": "library", "autoload": {"psr-4": {"MatthiasMullie\\PathConverter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.mullie.eu", "role": "Developer"}], "description": "Relative path converter", "homepage": "http://github.com/matthiasmullie/path-converter", "keywords": ["converter", "path", "paths", "relative"], "support": {"issues": "https://github.com/matthiasmullie/path-converter/issues", "source": "https://github.com/matthiasmullie/path-converter/tree/1.1.3"}, "time": "2019-02-05T23:41:09+00:00"}, {"name": "mck89/peast", "version": "v1.16.3", "source": {"type": "git", "url": "https://github.com/mck89/peast.git", "reference": "645ec21b650bc2aced18285c85f220d22afc1430"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mck89/peast/zipball/645ec21b650bc2aced18285c85f220d22afc1430", "reference": "645ec21b650bc2aced18285c85f220d22afc1430", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.16.3-dev"}}, "autoload": {"psr-4": {"Peast\\": "lib/Peast/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Peast is PHP library that generates AST for JavaScript code", "support": {"issues": "https://github.com/mck89/peast/issues", "source": "https://github.com/mck89/peast/tree/v1.16.3"}, "time": "2024-07-23T14:00:32+00:00"}, {"name": "meilisearch/meilisearch-php", "version": "v1.14.0", "source": {"type": "git", "url": "https://github.com/meilisearch/meilisearch-php.git", "reference": "860c6f81e81abb5040c50031512b21ae59a26a97"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/meilisearch/meilisearch-php/zipball/860c6f81e81abb5040c50031512b21ae59a26a97", "reference": "860c6f81e81abb5040c50031512b21ae59a26a97", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.4 || ^8.0", "php-http/discovery": "^1.7", "psr/http-client": "^1.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.8.1", "http-interop/http-factory-guzzle": "^1.2.0", "php-cs-fixer/shim": "^3.59.3", "phpstan/extension-installer": "^1.4.1", "phpstan/phpstan": "^2.0", "phpstan/phpstan-deprecation-rules": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.5 || ^10.5"}, "suggest": {"guzzlehttp/guzzle": "Use Guzzle ^7 as HTTP client", "http-interop/http-factory-guzzle": "Factory for guzzlehttp/guzzle"}, "type": "library", "autoload": {"psr-4": {"MeiliSearch\\": "src/", "Meilisearch\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP wrapper for the Meilisearch API", "keywords": ["api", "client", "instant", "meilisearch", "php", "search"], "support": {"issues": "https://github.com/meilisearch/meilisearch-php/issues", "source": "https://github.com/meilisearch/meilisearch-php/tree/v1.14.0"}, "time": "2025-04-14T12:32:57+00:00"}, {"name": "mglaman/phpstan-drupal", "version": "1.3.7", "source": {"type": "git", "url": "https://github.com/mglaman/phpstan-drupal.git", "reference": "91cb3860d816316dd98503ef258bc386f5fc22b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mglaman/phpstan-drupal/zipball/91cb3860d816316dd98503ef258bc386f5fc22b7", "reference": "91cb3860d816316dd98503ef258bc386f5fc22b7", "shasum": ""}, "require": {"php": "^8.1", "phpstan/phpstan": "^1.12", "phpstan/phpstan-deprecation-rules": "^1.1.4", "symfony/finder": "^4.2 || ^5.0 || ^6.0 || ^7.0", "symfony/yaml": "^4.2|| ^5.0 || ^6.0 || ^7.0", "webflo/drupal-finder": "^1.3.1"}, "require-dev": {"behat/mink": "^1.8", "composer/installers": "^1.9", "drupal/core-recommended": "^10", "drush/drush": "^10.0 || ^11 || ^12 || ^13@beta", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^8.5 || ^9 || ^10 || ^11", "slevomat/coding-standard": "^7.1", "squizlabs/php_codesniffer": "^3.3", "symfony/phpunit-bridge": "^4.4 || ^5.4 || ^6.0 || ^7.0"}, "suggest": {"jangregor/phpstan-prophecy": "Provides a prophecy/prophecy extension for phpstan/phpstan.", "phpstan/phpstan-deprecation-rules": "For catching deprecations, especially in Drupal core.", "phpstan/phpstan-phpunit": "PHPUnit extensions and rules for PHPStan."}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["extension.neon", "rules.neon"]}, "branch-alias": {"dev-main": "1.0-dev"}, "installer-paths": {"tests/fixtures/drupal/core": ["type:drupal-core"], "tests/fixtures/drupal/libraries/{$name}": ["type:drupal-library"], "tests/fixtures/drupal/themes/contrib/{$name}": ["type:drupal-theme"], "tests/fixtures/drupal/modules/contrib/{$name}": ["type:drupal-module"], "tests/fixtures/drupal/profiles/contrib/{$name}": ["type:drupal-profile"]}}, "autoload": {"psr-4": {"mglaman\\PHPStanDrupal\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Drupal extension and rules for PHPStan", "support": {"issues": "https://github.com/mglaman/phpstan-drupal/issues", "source": "https://github.com/mglaman/phpstan-drupal/tree/1.3.7"}, "funding": [{"url": "https://github.com/mglaman", "type": "github"}, {"url": "https://opencollective.com/phpstan-drupal", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/mglaman/phpstan-drupal", "type": "tidelift"}], "time": "2025-04-15T16:10:17+00:00"}, {"name": "nikic/php-parser", "version": "v5.4.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "447a020a1f875a434d62f2a401f53b82a396e494"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/447a020a1f875a434d62f2a401f53b82a396e494", "reference": "447a020a1f875a434d62f2a401f53b82a396e494", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.4.0"}, "time": "2024-12-30T11:07:19+00:00"}, {"name": "pear/archive_tar", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/pear/Archive_Tar.git", "reference": "b439c859564f5cbb0f64ad6002d0afe84a889602"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/Archive_Tar/zipball/b439c859564f5cbb0f64ad6002d0afe84a889602", "reference": "b439c859564f5cbb0f64ad6002d0afe84a889602", "shasum": ""}, "require": {"pear/pear-core-minimal": "^1.10.0alpha2", "php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-bz2": "Bz2 compression support.", "ext-xz": "Lzma2 compression support.", "ext-zlib": "Gzip compression support."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-0": {"Archive_Tar": ""}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["./"], "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Tar file management class with compression support (gzip, bzip2, lzma2)", "homepage": "https://github.com/pear/Archive_Tar", "keywords": ["archive", "tar"], "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=Archive_Tar", "source": "https://github.com/pear/Archive_Tar"}, "time": "2024-03-16T16:21:40+00:00"}, {"name": "pear/console_getopt", "version": "v1.4.3", "source": {"type": "git", "url": "https://github.com/pear/Console_Getopt.git", "reference": "a41f8d3e668987609178c7c4a9fe48fecac53fa0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/Console_Getopt/zipball/a41f8d3e668987609178c7c4a9fe48fecac53fa0", "reference": "a41f8d3e668987609178c7c4a9fe48fecac53fa0", "shasum": ""}, "type": "library", "autoload": {"psr-0": {"Console": "./"}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["./"], "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead"}, {"name": "Stig Bakken", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Helper"}], "description": "More info available on: http://pear.php.net/package/Console_<PERSON>opt", "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=Console_Getopt", "source": "https://github.com/pear/Console_<PERSON>opt"}, "time": "2019-11-20T18:27:48+00:00"}, {"name": "pear/pear-core-minimal", "version": "v1.10.16", "source": {"type": "git", "url": "https://github.com/pear/pear-core-minimal.git", "reference": "c0f51b45f50683bf5bbf558036854ebc9b54d033"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/pear-core-minimal/zipball/c0f51b45f50683bf5bbf558036854ebc9b54d033", "reference": "c0f51b45f50683bf5bbf558036854ebc9b54d033", "shasum": ""}, "require": {"pear/console_getopt": "~1.4", "pear/pear_exception": "~1.0", "php": ">=5.4"}, "replace": {"rsky/pear-core-min": "self.version"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": ["src/"], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead"}], "description": "Minimal set of PEAR core files to be used as composer dependency", "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=PEAR", "source": "https://github.com/pear/pear-core-minimal"}, "time": "2024-11-24T22:27:58+00:00"}, {"name": "pear/pear_exception", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/pear/PEAR_Exception.git", "reference": "b14fbe2ddb0b9f94f5b24cf08783d599f776fff0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/PEAR_Exception/zipball/b14fbe2ddb0b9f94f5b24cf08783d599f776fff0", "reference": "b14fbe2ddb0b9f94f5b24cf08783d599f776fff0", "shasum": ""}, "require": {"php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "<9"}, "type": "class", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["PEAR/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": ["."], "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The PEAR Exception base class.", "homepage": "https://github.com/pear/PEAR_Exception", "keywords": ["exception"], "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=PEAR_Exception", "source": "https://github.com/pear/PEAR_Exception"}, "time": "2021-03-21T15:43:46+00:00"}, {"name": "phootwork/collection", "version": "v3.2.3", "source": {"type": "git", "url": "https://github.com/phootwork/collection.git", "reference": "46dde20420fba17766c89200bc3ff91d3e58eafa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phootwork/collection/zipball/46dde20420fba17766c89200bc3ff91d3e58eafa", "reference": "46dde20420fba17766c89200bc3ff91d3e58eafa", "shasum": ""}, "require": {"phootwork/lang": "^3.0", "php": ">=8.0"}, "type": "library", "autoload": {"psr-4": {"phootwork\\collection\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://gos.si"}], "description": "The phootwork library fills gaps in the php language and provides better solutions than the existing ones php offers.", "homepage": "https://phootwork.github.io/collection/", "keywords": ["Array object", "Text object", "collection", "collections", "json", "list", "map", "queue", "set", "stack", "xml"], "support": {"issues": "https://github.com/phootwork/phootwork/issues", "source": "https://github.com/phootwork/collection/tree/v3.2.3"}, "time": "2022-08-27T12:51:24+00:00"}, {"name": "phootwork/lang", "version": "v3.2.3", "source": {"type": "git", "url": "https://github.com/phootwork/lang.git", "reference": "52ec8cce740ce1c424eef02f43b43d5ddfec7b5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phootwork/lang/zipball/52ec8cce740ce1c424eef02f43b43d5ddfec7b5e", "reference": "52ec8cce740ce1c424eef02f43b43d5ddfec7b5e", "shasum": ""}, "require": {"php": ">=8.0", "symfony/polyfill-mbstring": "^1.12", "symfony/polyfill-php81": "^1.22"}, "type": "library", "autoload": {"psr-4": {"phootwork\\lang\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://gos.si"}], "description": "Missing PHP language constructs", "homepage": "https://phootwork.github.io/lang/", "keywords": ["array", "comparator", "comparison", "string"], "support": {"issues": "https://github.com/phootwork/phootwork/issues", "source": "https://github.com/phootwork/lang/tree/v3.2.3"}, "time": "2024-10-03T13:43:19+00:00"}, {"name": "php-http/discovery", "version": "1.20.0", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/82fe4c73ef3363caed49ff8dd1539ba06044910d", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "sebastian/comparator": "^3.0.5 || ^4.0.8", "symfony/phpunit-bridge": "^6.4.4 || ^7.0.1"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.20.0"}, "time": "2024-10-02T11:20:13+00:00"}, {"name": "php-tuf/composer-stager", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/php-tuf/composer-stager.git", "reference": "95ea147ae5eccb4ac19fd9b978549634bb9f46d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-tuf/composer-stager/zipball/95ea147ae5eccb4ac19fd9b978549634bb9f46d4", "reference": "95ea147ae5eccb4ac19fd9b978549634bb9f46d4", "shasum": ""}, "require": {"ext-json": "*", "php": ">=8.1.0", "symfony/filesystem": "^6.2 || ^7.0", "symfony/process": "^6.4.14 || ^7.1.7", "symfony/translation-contracts": "^3.1"}, "conflict": {"symfony/process": ">=6 <6.4.14 || >=7 <7.1.7", "symfony/symfony": ">=6 <6.4.14 || >=7 <7.1.7"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "ext-simplexml": "*", "phpspec/prophecy": "^1.17", "phpspec/prophecy-phpunit": "^2.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5.19", "slevomat/coding-standard": "^8.13", "squizlabs/php_codesniffer": "^3.7", "symfony/config": "^6.3", "symfony/dependency-injection": "^6.3", "symfony/yaml": "^6.3", "thecodingmachine/phpstan-strict-rules": "^1.0"}, "suggest": {"symfony/dependency-injection": "For dependency injection", "symfony/translation": "For internationalization tools"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "2.x-dev"}}, "autoload": {"psr-4": {"PhpTuf\\ComposerStager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Stages Composer commands so they can be safely run on a production codebase.", "homepage": "https://github.com/php-tuf/composer-stager", "support": {"issues": "https://github.com/php-tuf/composer-stager/issues", "source": "https://github.com/php-tuf/composer-stager"}, "time": "2025-04-21T15:27:20+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "2.3.8", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "7a700683743bf1c4a21837c84b266916f1aa7d25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/7a700683743bf1c4a21837c84b266916f1aa7d25", "reference": "7a700683743bf1c4a21837c84b266916f1aa7d25", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "maennchen/zipstream-php": "^2.1 || ^3.0", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^8.1", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-main", "dompdf/dompdf": "^2.0 || ^3.0", "friendsofphp/php-cs-fixer": "^3.2", "mitoteam/jpgraph": "^10.3", "mpdf/mpdf": "^8.1.1", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.6 || ^10.5", "squizlabs/php_codesniffer": "^3.7", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "ext-intl": "PHP Internationalization Functions", "mitoteam/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/2.3.8"}, "time": "2025-02-08T03:01:45+00:00"}, {"name": "phpowermove/docblock", "version": "v4.0", "source": {"type": "git", "url": "https://github.com/phpowermove/docblock.git", "reference": "a73f6e17b7d4e1b92ca5378c248c952c9fae7826"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpowermove/docblock/zipball/a73f6e17b7d4e1b92ca5378c248c952c9fae7826", "reference": "a73f6e17b7d4e1b92ca5378c248c952c9fae7826", "shasum": ""}, "require": {"phootwork/collection": "^3.0", "phootwork/lang": "^3.0", "php": ">=8.0"}, "require-dev": {"phootwork/php-cs-fixer-config": "^0.4", "phpunit/phpunit": "^9.0", "psalm/phar": "^4.3"}, "type": "library", "autoload": {"psr-4": {"phpowermove\\docblock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://gos.si"}], "description": "PHP Docblock parser and generator. An API to read and write Docblocks.", "keywords": ["doc<PERSON>", "generator", "parser"], "support": {"issues": "https://github.com/phpowermove/docblock/issues", "source": "https://github.com/phpowermove/docblock/tree/v4.0"}, "time": "2021-09-22T16:57:06+00:00"}, {"name": "phpstan/phpstan", "version": "1.12.26", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "84cbf8f018e01834b9b1ac3dacf3b9780e209e53"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/84cbf8f018e01834b9b1ac3dacf3b9780e209e53", "reference": "84cbf8f018e01834b9b1ac3dacf3b9780e209e53", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}], "time": "2025-05-14T11:08:32+00:00"}, {"name": "phpstan/phpstan-deprecation-rules", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan-deprecation-rules.git", "reference": "f94d246cc143ec5a23da868f8f7e1393b50eaa82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan-deprecation-rules/zipball/f94d246cc143ec5a23da868f8f7e1393b50eaa82", "reference": "f94d246cc143ec5a23da868f8f7e1393b50eaa82", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpstan/phpstan": "^1.12"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.5"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["rules.neon"]}}, "autoload": {"psr-4": {"PHPStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan rules for detecting usage of deprecated classes, methods, properties, constants and traits.", "support": {"issues": "https://github.com/phpstan/phpstan-deprecation-rules/issues", "source": "https://github.com/phpstan/phpstan-deprecation-rules/tree/1.2.1"}, "time": "2024-09-11T15:52:35+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "time": "2021-10-29T13:26:27+00:00"}, {"name": "psy/psysh", "version": "v0.12.8", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "85057ceedee50c49d4f6ecaff73ee96adb3b3625"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/85057ceedee50c49d4f6ecaff73ee96adb3b3625", "reference": "85057ceedee50c49d4f6ecaff73ee96adb3b3625", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "^5.0 || ^4.0", "php": "^8.0 || ^7.4", "symfony/console": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4"}, "conflict": {"symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well."}, "bin": ["bin/psysh"], "type": "library", "extra": {"bamarni-bin": {"bin-links": false, "forward-command": false}, "branch-alias": {"dev-main": "0.12.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.8"}, "time": "2025-03-16T03:05:19+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "revolt/event-loop", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/revoltphp/event-loop.git", "reference": "09bf1bf7f7f574453efe43044b06fafe12216eb3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/revoltphp/event-loop/zipball/09bf1bf7f7f574453efe43044b06fafe12216eb3", "reference": "09bf1bf7f7f574453efe43044b06fafe12216eb3", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"ext-json": "*", "jetbrains/phpstorm-stubs": "^2019.3", "phpunit/phpunit": "^9", "psalm/phar": "^5.15"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Revolt\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Rock-solid event loop for concurrent PHP applications.", "keywords": ["async", "asynchronous", "concurrency", "event", "event-loop", "non-blocking", "scheduler"], "support": {"issues": "https://github.com/revoltphp/event-loop/issues", "source": "https://github.com/revoltphp/event-loop/tree/v1.0.7"}, "time": "2025-01-25T19:27:39+00:00"}, {"name": "sebastian/diff", "version": "5.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/c41e007b4b62af48218231d6c2275e4c9b975b2e", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0", "symfony/process": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "security": "https://github.com/sebastian<PERSON>mann/diff/security/policy", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/5.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:15:17+00:00"}, {"name": "solarium/solarium", "version": "6.3.7", "source": {"type": "git", "url": "https://github.com/solariumphp/solarium.git", "reference": "4f3cb22a4d98df2c8d5a621ad1beb93fad7b94c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/solariumphp/solarium/zipball/4f3cb22a4d98df2c8d5a621ad1beb93fad7b94c5", "reference": "4f3cb22a4d98df2c8d5a621ad1beb93fad7b94c5", "shasum": ""}, "require": {"composer-runtime-api": ">=2.0", "ext-json": "*", "halaxa/json-machine": "^1.1", "php": "^8.1", "psr/event-dispatcher": "^1.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "symfony/event-dispatcher-contracts": "^2.0 || ^3.0"}, "require-dev": {"escapestudios/symfony2-coding-standard": "^3.11", "ext-curl": "*", "ext-iconv": "*", "nyholm/psr7": "^1.8", "php-http/guzzle7-adapter": "^1.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^10.5", "rawr/phpunit-data-provider": "^3.3", "roave/security-advisories": "dev-master", "symfony/event-dispatcher": "^5.0 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Solarium\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "See GitHub contributors", "homepage": "https://github.com/solariumphp/solarium/contributors"}], "description": "PHP Solr client", "homepage": "http://www.solarium-project.org", "keywords": ["php", "search", "solr"], "support": {"issues": "https://github.com/solariumphp/solarium/issues", "source": "https://github.com/solariumphp/solarium/tree/6.3.7"}, "time": "2025-02-20T10:29:08+00:00"}, {"name": "symfony/console", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "0e2e3f38c192e93e622e41ec37f4ca70cfedf218"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/0e2e3f38c192e93e622e41ec37f4ca70cfedf218", "reference": "0e2e3f38c192e93e622e41ec37f4ca70cfedf218", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^6.4|^7.0"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/dotenv": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/lock": "<6.4", "symfony/process": "<6.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-07T19:09:28+00:00"}, {"name": "symfony/dependency-injection", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "2ca85496cde37f825bd14f7e3548e2793ca90712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/2ca85496cde37f825bd14f7e3548e2793ca90712", "reference": "2ca85496cde37f825bd14f7e3548e2793ca90712", "shasum": ""}, "require": {"php": ">=8.2", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^3.5", "symfony/var-exporter": "^6.4.20|^7.2.5"}, "conflict": {"ext-psr": "<1.1|>=2", "symfony/config": "<6.4", "symfony/finder": "<6.4", "symfony/yaml": "<6.4"}, "provide": {"psr/container-implementation": "1.1|2.0", "symfony/service-implementation": "1.1|2.0|3.0"}, "require-dev": {"symfony/config": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-27T13:37:55+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/error-handler", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "102be5e6a8e4f4f3eb3149bcbfa33a80d1ee374b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/102be5e6a8e4f4f3eb3149bcbfa33a80d1ee374b", "reference": "102be5e6a8e4f4f3eb3149bcbfa33a80d1ee374b", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^6.4|^7.0"}, "conflict": {"symfony/deprecation-contracts": "<2.5", "symfony/http-kernel": "<6.4"}, "require-dev": {"symfony/deprecation-contracts": "^2.5|^3", "symfony/http-kernel": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-03T07:12:39+00:00"}, {"name": "symfony/event-dispatcher", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/910c5db85a5356d0fea57680defec4e99eb9c8c1", "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1", "shasum": ""}, "require": {"php": ">=8.2", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/error-handler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7642f5e970b672283b7823222ae8ef8bbc160b9f", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/filesystem", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "b8dce482de9d7c9fe2891155035a7248ab5c7fdb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/b8dce482de9d7c9fe2891155035a7248ab5c7fdb", "reference": "b8dce482de9d7c9fe2891155035a7248ab5c7fdb", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-25T15:15:23+00:00"}, {"name": "symfony/finder", "version": "v7.2.2", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "87a71856f2f56e4100373e92529eed3171695cfb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/87a71856f2f56e4100373e92529eed3171695cfb", "reference": "87a71856f2f56e4100373e92529eed3171695cfb", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v7.2.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-30T19:00:17+00:00"}, {"name": "symfony/http-foundation", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "6023ec7607254c87c5e69fb3558255aca440d72b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/6023ec7607254c87c5e69fb3558255aca440d72b", "reference": "6023ec7607254c87c5e69fb3558255aca440d72b", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"doctrine/dbal": "<3.6", "symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^3.6|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-09T08:14:01+00:00"}, {"name": "symfony/http-kernel", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "f9dec01e6094a063e738f8945ef69c0cfcf792ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/f9dec01e6094a063e738f8945ef69c0cfcf792ec", "reference": "f9dec01e6094a063e738f8945ef69c0cfcf792ec", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/browser-kit": "<6.4", "symfony/cache": "<6.4", "symfony/config": "<6.4", "symfony/console": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<6.4", "symfony/form": "<6.4", "symfony/http-client": "<6.4", "symfony/http-client-contracts": "<2.5", "symfony/mailer": "<6.4", "symfony/messenger": "<6.4", "symfony/translation": "<6.4", "symfony/translation-contracts": "<2.5", "symfony/twig-bridge": "<6.4", "symfony/validator": "<6.4", "symfony/var-dumper": "<6.4", "twig/twig": "<3.12"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^6.4|^7.0", "symfony/clock": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/css-selector": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/dom-crawler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^7.1", "symfony/routing": "^6.4|^7.0", "symfony/serializer": "^7.1", "symfony/stopwatch": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0", "symfony/var-exporter": "^6.4|^7.0", "twig/twig": "^3.12"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-02T09:04:03+00:00"}, {"name": "symfony/mailer", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "998692469d6e698c6eadc7ef37a6530a9eabb356"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/998692469d6e698c6eadc7ef37a6530a9eabb356", "reference": "998692469d6e698c6eadc7ef37a6530a9eabb356", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.2", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/mime": "^7.2", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/messenger": "<6.4", "symfony/mime": "<6.4", "symfony/twig-bridge": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/twig-bridge": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-04T09:50:51+00:00"}, {"name": "symfony/mime", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "706e65c72d402539a072d0d6ad105fff6c161ef1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/706e65c72d402539a072d0d6ad105fff6c161ef1", "reference": "706e65c72d402539a072d0d6ad105fff6c161ef1", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<6.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-27T13:34:41+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "48becf00c920479ca2e910c22a5a39e5d47ca956"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/48becf00c920479ca2e910c22a5a39e5d47ca956", "reference": "48becf00c920479ca2e910c22a5a39e5d47ca956", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/c36586dcf89a12315939e00ec9b4474adcb1d773", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/process", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "87b7c93e57df9d8e39a093d32587702380ff045d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/87b7c93e57df9d8e39a093d32587702380ff045d", "reference": "87b7c93e57df9d8e39a093d32587702380ff045d", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-13T12:21:46+00:00"}, {"name": "symfony/psr-http-message-bridge", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/psr-http-message-bridge.git", "reference": "03f2f72319e7acaf2a9f6fcbe30ef17eec51594f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/03f2f72319e7acaf2a9f6fcbe30ef17eec51594f", "reference": "03f2f72319e7acaf2a9f6fcbe30ef17eec51594f", "shasum": ""}, "require": {"php": ">=8.2", "psr/http-message": "^1.0|^2.0", "symfony/http-foundation": "^6.4|^7.0"}, "conflict": {"php-http/discovery": "<1.15", "symfony/http-kernel": "<6.4"}, "require-dev": {"nyholm/psr7": "^1.1", "php-http/discovery": "^1.15", "psr/log": "^1.1.4|^2|^3", "symfony/browser-kit": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/framework-bundle": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\PsrHttpMessage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "PSR HTTP message bridge", "homepage": "https://symfony.com", "keywords": ["http", "http-message", "psr-17", "psr-7"], "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-26T08:57:56+00:00"}, {"name": "symfony/routing", "version": "v7.2.3", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "ee9a67edc6baa33e5fae662f94f91fd262930996"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/ee9a67edc6baa33e5fae662f94f91fd262930996", "reference": "ee9a67edc6baa33e5fae662f94f91fd262930996", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"symfony/config": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/yaml": "<6.4"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v7.2.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-17T10:56:55+00:00"}, {"name": "symfony/serializer", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "be549655b034edc1a16ed23d8164aa04318c5ec1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/be549655b034edc1a16ed23d8164aa04318c5ec1", "reference": "be549655b034edc1a16ed23d8164aa04318c5ec1", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<6.4", "symfony/property-access": "<6.4", "symfony/property-info": "<6.4", "symfony/uid": "<6.4", "symfony/validator": "<6.4", "symfony/yaml": "<6.4"}, "require-dev": {"phpdocumentor/reflection-docblock": "^3.2|^4.0|^5.0", "phpstan/phpdoc-parser": "^1.0|^2.0", "seld/jsonlint": "^1.10", "symfony/cache": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^7.2", "symfony/error-handler": "^6.4|^7.0", "symfony/filesystem": "^6.4|^7.0", "symfony/form": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/type-info": "^7.1", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0", "symfony/var-exporter": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Handles serializing and deserializing data structures, including object graphs, into array structures or other formats like XML and JSON.", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/serializer/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-27T13:34:41+00:00"}, {"name": "symfony/service-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/string", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "a214fe7d62bd4df2a76447c67c6b26e1d5e74931"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/a214fe7d62bd4df2a76447c67c6b26e1d5e74931", "reference": "a214fe7d62bd4df2a76447c67c6b26e1d5e74931", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-20T20:18:16+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/4667ff3bd513750603a09c8dedbea942487fb07c", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/validator", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "f7c32e309885a97fc9572335e22c2c2d31f328c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/f7c32e309885a97fc9572335e22c2c2d31f328c4", "reference": "f7c32e309885a97fc9572335e22c2c2d31f328c4", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php83": "^1.27", "symfony/translation-contracts": "^2.5|^3"}, "conflict": {"doctrine/lexer": "<1.1", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<7.0", "symfony/expression-language": "<6.4", "symfony/http-kernel": "<6.4", "symfony/intl": "<6.4", "symfony/property-info": "<6.4", "symfony/translation": "<6.4.3|>=7.0,<7.0.3", "symfony/yaml": "<6.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3|^4", "symfony/cache": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/translation": "^6.4.3|^7.0.3", "symfony/type-info": "^7.1", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/", "/Resources/bin/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-02T08:36:00+00:00"}, {"name": "symfony/var-dumper", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "9c46038cd4ed68952166cf7001b54eb539184ccb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/9c46038cd4ed68952166cf7001b54eb539184ccb", "reference": "9c46038cd4ed68952166cf7001b54eb539184ccb", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/uid": "^6.4|^7.0", "twig/twig": "^3.12"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-09T08:14:01+00:00"}, {"name": "symfony/var-exporter", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "422b8de94c738830a1e071f59ad14d67417d7007"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/422b8de94c738830a1e071f59ad14d67417d7007", "reference": "422b8de94c738830a1e071f59ad14d67417d7007", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-02T08:36:00+00:00"}, {"name": "symfony/yaml", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "0feafffb843860624ddfd13478f481f4c3cd8b23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/0feafffb843860624ddfd13478f481f4c3cd8b23", "reference": "0feafffb843860624ddfd13478f481f4c3cd8b23", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-04T10:10:11+00:00"}, {"name": "twig/twig", "version": "v3.19.0", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "d4f8c2b86374f08efc859323dbcd95c590f7124e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/d4f8c2b86374f08efc859323dbcd95c590f7124e", "reference": "d4f8c2b86374f08efc859323dbcd95c590f7124e", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php81": "^1.29"}, "require-dev": {"phpstan/phpstan": "^2.0", "psr/container": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.4.9|^6.4|^7.0"}, "type": "library", "autoload": {"files": ["src/Resources/core.php", "src/Resources/debug.php", "src/Resources/escaper.php", "src/Resources/string_loader.php"], "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.19.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2025-01-29T07:06:14+00:00"}, {"name": "webflo/drupal-finder", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/webflo/drupal-finder.git", "reference": "73045060b0894c77962a10cff047f72872d8810c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webflo/drupal-finder/zipball/73045060b0894c77962a10cff047f72872d8810c", "reference": "73045060b0894c77962a10cff047f72872d8810c", "shasum": ""}, "require": {"composer-runtime-api": "^2.2", "php": ">=8.1"}, "require-dev": {"mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^10.4", "symfony/process": "^6.4"}, "type": "library", "autoload": {"psr-4": {"DrupalFinder\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Helper class to locate a <PERSON><PERSON><PERSON> installation.", "support": {"issues": "https://github.com/webflo/drupal-finder/issues", "source": "https://github.com/webflo/drupal-finder/tree/1.3.1"}, "time": "2024-06-28T13:45:36+00:00"}], "packages-dev": [{"name": "composer/xdebug-handler", "version": "3.0.5", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/6c1925561632e83d60a44492e0b344cf48ab85ef", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8.5 || ^9.6 || ^10.5"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-05-06T16:37:16+00:00"}, {"name": "jangregor/phpstan-prophecy", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/Jan0707/phpstan-prophecy.git", "reference": "5ee56c7db1d58f0578c82a35e3c1befe840e85a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jan0707/phpstan-prophecy/zipball/5ee56c7db1d58f0578c82a35e3c1befe840e85a9", "reference": "5ee56c7db1d58f0578c82a35e3c1befe840e85a9", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "phpstan/phpstan": "^1.0.0"}, "conflict": {"phpspec/prophecy": "<1.7.0 || >=2.0.0", "phpunit/phpunit": "<6.0.0 || >=12.0.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.1.1", "ergebnis/license": "^1.0.0", "ergebnis/php-cs-fixer-config": "~2.2.0", "phpspec/prophecy": "^1.7.0", "phpunit/phpunit": "^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"JanGregor\\Prophecy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>-<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Provides a phpstan/phpstan extension for phpspec/prophecy", "support": {"issues": "https://github.com/Jan0707/phpstan-prophecy/issues", "source": "https://github.com/Jan0707/phpstan-prophecy/tree/1.0.2"}, "time": "2024-04-03T08:15:54+00:00"}, {"name": "jean85/pretty-package-versions", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/Jean85/pretty-package-versions.git", "reference": "4d7aa5dab42e2a76d99559706022885de0e18e1a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jean85/pretty-package-versions/zipball/4d7aa5dab42e2a76d99559706022885de0e18e1a", "reference": "4d7aa5dab42e2a76d99559706022885de0e18e1a", "shasum": ""}, "require": {"composer-runtime-api": "^2.1.0", "php": "^7.4|^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "jean85/composer-provided-replaced-stub-package": "^1.0", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^7.5|^8.5|^9.6", "rector/rector": "^2.0", "vimeo/psalm": "^4.3 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Jean85\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to get pretty versions strings of installed dependencies", "keywords": ["composer", "package", "release", "versions"], "support": {"issues": "https://github.com/Jean85/pretty-package-versions/issues", "source": "https://github.com/Jean85/pretty-package-versions/tree/2.1.1"}, "time": "2025-03-19T14:43:43+00:00"}, {"name": "mglaman/drupal-check", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/mglaman/drupal-check.git", "reference": "4011f1f357bdd89793d13b1f8536625eb9d3cce7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mglaman/drupal-check/zipball/4011f1f357bdd89793d13b1f8536625eb9d3cce7", "reference": "4011f1f357bdd89793d13b1f8536625eb9d3cce7", "shasum": ""}, "require": {"composer/xdebug-handler": "^1.1 || ^2.0.1 || ^3.0", "jangregor/phpstan-prophecy": "^1.0", "jean85/pretty-package-versions": "^1.5.0 || ^2.0.1", "mglaman/phpstan-drupal": "^1.0.0", "nette/neon": "^3.1", "php": "^7.2.5|^8.0", "phpstan/phpstan-deprecation-rules": "^1.0.0", "symfony/console": "~3.4.5 || ^4.2|| ^5.0 || ^6.0 || ^7.0", "symfony/process": "~3.4.5 || ^4.2|| ^5.0 || ^6.0 || ^7.0", "webflo/drupal-finder": "^1.1"}, "require-dev": {"phpstan/phpstan": "^1.0.0", "phpstan/phpstan-strict-rules": "^1.0.0", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["drupal-check"], "type": "project", "extra": {"violinist": {"one_pull_request_per_package": 1}}, "autoload": {"psr-4": {"DrupalCheck\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "CLI tool for running checks on a Drupal code base", "support": {"issues": "https://github.com/mglaman/drupal-check/issues", "source": "https://github.com/mglaman/drupal-check/tree/1.5.0"}, "funding": [{"url": "https://github.com/mglaman", "type": "github"}, {"url": "https://opencollective.com/phpstan-drupal", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/mglaman/drupal-check", "type": "tidelift"}], "time": "2024-08-14T21:40:06+00:00"}, {"name": "nette/neon", "version": "v3.4.4", "source": {"type": "git", "url": "https://github.com/nette/neon.git", "reference": "3411aa86b104e2d5b7e760da4600865ead963c3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/neon/zipball/3411aa86b104e2d5b7e760da4600865ead963c3c", "reference": "3411aa86b104e2d5b7e760da4600865ead963c3c", "shasum": ""}, "require": {"ext-json": "*", "php": "8.0 - 8.4"}, "require-dev": {"nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.7"}, "bin": ["bin/neon-lint"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🍸 Nette NEON: encodes and decodes NEON file format.", "homepage": "https://ne-on.org", "keywords": ["export", "import", "neon", "nette", "yaml"], "support": {"issues": "https://github.com/nette/neon/issues", "source": "https://github.com/nette/neon/tree/v3.4.4"}, "time": "2024-10-04T22:00:08+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"drupal/webform": 10, "drupal/webp": 5}, "prefer-stable": true, "prefer-lowest": false, "platform": {}, "platform-dev": {}, "plugin-api-version": "2.6.0"}